# SideView Product Requirements

## 1. What is SideView?
SideView is a slim browser panel that lives at the left or right edge of your screen.
Hover your mouse (or hit a hot-key) and the panel glides in, giving you instant access to the web apps and sites you use all day—Slack, Gmail, Notion, WhatsApp Web, trading dashboards, anything. Slide it away and you're back to your primary window with zero desktop clutter.

## 2. Why try it?
- **Stay in flow** – Check messages, docs, or dashboards without Alt-Tabbing or browser-tab hunting.
- **Save space** – A collapsible strip replaces always-open browser windows.
- **Multi-account sanity** – Run each site in its own cookie container so work and personal log-ins never clash.
- **Looks native** – Fluent-style UI, dark/light themes that track Windows, and buttery 60 fps animation.

## 3. What you'll see in this test build

| Area | What to look for | Tips for testers |
|------|------------------|------------------|
| Panel activation | Hover strip (default = 4 px) or Ctrl+Alt+ →/← hot-key. Smooth slide-in/out at edge you choose. | Try different DPI monitors and multi-display setups. Does the hover strip feel right? |
| App bar | Vertically stacked icons (auto-fetched favicons). Click to switch, right-click for context menu. | Add 10–15 apps; drag to reorder; rename; delete. |
| Browser view | Chromium-powered (Edge WebView 2). Standard Back/Forward/Reload buttons and an address bar. | Test heavy sites (e.g., YouTube) vs. light ones; measure memory in Task Manager. |
| Pin & auto-hide | Click the pin icon to keep the panel open; unpin and it hides when focus leaves. | Verify it doesn't steal focus while gaming or presenting. |
| Themes & sizing | Settings → change panel width (min 300 → max 800 px), toggle dark/light, set accent color. | Confirm title-bar, tray icon, and Windows notifications follow the theme. |
| Session modes | Shared (all apps share cookies) or Isolated per-app. | Log into two Gmail accounts in separate isolated apps; ensure sessions stay separate after restart. |
| Notifications | Web push (e.g., Slack) surface as native Windows toast; unread badge on icon. | Mute notifications globally and per-app; check they respect your setting. |
| Hot-keys | Global toggle, new-tab, and refresh shortcuts are remappable. | Clash-test with other apps' shortcuts. |

## 4. Known limitations in this build
- Picture-in-Picture, ad-blocking, and workspace presets are not enabled yet.
- Touch/pen edge swipe is experimental—please report but expect rough edges.
- Automatic update channel is wired, but you may need a manual restart to apply the next build.


