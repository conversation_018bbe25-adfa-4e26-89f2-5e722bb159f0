{"name": "sideview-electron", "version": "1.0.0", "description": "SideView - A sliding sidebar panel for quick access to web applications", "main": "dist/main/main.js", "scripts": {"build": "npm run build:main && npm run build:renderer", "build:main": "webpack --config webpack.main.config.js", "build:renderer": "webpack --config webpack.renderer.config.js", "dev": "npm run build && electron .", "dev:watch": "concurrently \"npm run build:main -- --watch\" \"npm run build:renderer -- --watch\" \"wait-on dist/main/main.js && electron .\"", "start": "electron .", "pack": "electron-builder --dir", "dist": "electron-builder", "dist:win": "electron-builder --win", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "keywords": ["electron", "sidebar", "web-apps", "productivity", "windows"], "author": "SideView Team", "license": "MIT", "devDependencies": {"@types/better-sqlite3": "^7.6.8", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "concurrently": "^8.2.2", "copy-webpack-plugin": "^13.0.0", "electron": "^36.3.2", "electron-builder": "^24.6.4", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "typescript": "^5.2.2", "wait-on": "^7.2.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@octokit/rest": "^20.0.2", "better-sqlite3": "^11.10.0", "cheerio": "^1.0.0-rc.12", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "globalthis": "^1.0.3", "node-fetch": "^3.3.2"}, "build": {"appId": "com.sideview.app", "productName": "SideView", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*", "package.json"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}