# SideView.Core Module Implementation

## Overview
The Core module serves as the foundation and orchestrator for the entire SideView application. It manages the application lifecycle, dependency injection, configuration, and inter-module communication through an event bus system.

## Architecture

```mermaid
graph TB
    A[Application Entry Point] --> B[Generic Host Builder]
    B --> C[Service Registration]
    C --> D[Configuration Loading]
    D --> E[Logging Setup]
    E --> F[Event Bus Initialization]
    F --> G[Module Startup]
    G --> H[Application Ready]
    
    subgraph "Services"
        I[IAppEventBus]
        J[IConfigurationService]
        K[ILoggingService]
        L[ILifecycleManager]
    end
    
    C --> I
    C --> J
    C --> K
    C --> L
```

## Key Components

### 1. Application Host (`AppHost.cs`)

```csharp
public class AppHost
{
    private readonly IHost _host;
    private readonly ILogger<AppHost> _logger;
    
    public static async Task<AppHost> CreateAsync(string[] args)
    {
        var host = Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration(ConfigureApp)
            .ConfigureServices(ConfigureServices)
            .ConfigureLogging(ConfigureLogging)
            .UseWindowsService() // Optional for service mode
            .Build();
            
        return new AppHost(host);
    }
}
```

### 2. Event Bus System (`AppEventBus.cs`)

```mermaid
sequenceDiagram
    participant P as Publisher
    participant E as EventBus
    participant S as Subscriber
    participant C as Channel
    
    P->>E: PublishAsync(event)
    E->>C: WriteAsync(event)
    C->>S: OnEventReceived(event)
    S->>S: Process Event
    S->>E: Acknowledgment (optional)
```

```csharp
public interface IAppEventBus
{
    Task PublishAsync<T>(T eventData, CancellationToken cancellationToken = default) where T : IAppEvent;
    IAsyncEnumerable<T> SubscribeAsync<T>(CancellationToken cancellationToken = default) where T : IAppEvent;
    void Subscribe<T>(Func<T, CancellationToken, Task> handler) where T : IAppEvent;
}

public class AppEventBus : IAppEventBus, IDisposable
{
    private readonly Channel<IAppEvent> _channel;
    private readonly ILogger<AppEventBus> _logger;
    private readonly ConcurrentDictionary<Type, List<Func<IAppEvent, CancellationToken, Task>>> _handlers;
    
    public AppEventBus(ILogger<AppEventBus> logger)
    {
        var options = new BoundedChannelOptions(1000)
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = false,
            SingleWriter = false
        };
        
        _channel = Channel.CreateBounded<IAppEvent>(options);
        _logger = logger;
        _handlers = new();
        
        // Start background processing
        _ = Task.Run(ProcessEventsAsync);
    }
    
    private async Task ProcessEventsAsync()
    {
        await foreach (var eventData in _channel.Reader.ReadAllAsync())
        {
            await DispatchEventAsync(eventData);
        }
    }
}
```

### 3. Configuration Management (`ConfigurationService.cs`)

```mermaid
flowchart TD
    A[Application Start] --> B{Config File Exists?}
    B -->|No| C[Create Default Config]
    B -->|Yes| D[Load Existing Config]
    C --> E[Validate Configuration]
    D --> E
    E --> F{Valid?}
    F -->|No| G[Log Warning & Use Defaults]
    F -->|Yes| H[Apply Configuration]
    G --> H
    H --> I[Start File Watcher]
    I --> J[Configuration Ready]
    
    subgraph "File Watcher"
        K[File Changed] --> L[Debounce Changes]
        L --> M[Reload Configuration]
        M --> N[Notify Subscribers]
    end
    
    I --> K
```

```csharp
public interface IConfigurationService
{
    AppSettings Settings { get; }
    event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;
    Task SaveAsync();
    Task ReloadAsync();
}

public class ConfigurationService : IConfigurationService, IDisposable
{
    private readonly string _configPath;
    private readonly FileSystemWatcher _watcher;
    private readonly SemaphoreSlim _semaphore;
    private readonly ILogger<ConfigurationService> _logger;
    private AppSettings _settings;
    
    public AppSettings Settings => _settings;
    public event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;
    
    public ConfigurationService(ILogger<ConfigurationService> logger)
    {
        _configPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "SideView", "settings.json");
            
        _semaphore = new SemaphoreSlim(1, 1);
        _logger = logger;
        
        InitializeAsync().GetAwaiter().GetResult();
        SetupFileWatcher();
    }
    
    private async Task InitializeAsync()
    {
        await _semaphore.WaitAsync();
        try
        {
            if (File.Exists(_configPath))
            {
                var json = await File.ReadAllTextAsync(_configPath);
                _settings = JsonSerializer.Deserialize<AppSettings>(json, JsonOptions) ?? AppSettings.Default;
            }
            else
            {
                _settings = AppSettings.Default;
                await SaveAsync();
            }
        }
        finally
        {
            _semaphore.Release();
        }
    }
}
```

## Application Lifecycle Flow

```mermaid
stateDiagram-v2
    [*] --> Initializing
    Initializing --> ConfiguringServices
    ConfiguringServices --> LoadingConfiguration
    LoadingConfiguration --> StartingModules
    StartingModules --> Running
    Running --> Stopping: Shutdown Signal
    Stopping --> CleaningUp
    CleaningUp --> [*]
    
    Running --> Reloading: Config Change
    Reloading --> Running
    
    note right of StartingModules
        Modules start in order:
        1. Core
        2. UI
        3. WebEngine
        4. AppManager
        5. SessionMgr
        6. Notifications
        7. Hotkeys
        8. Updater
    end note
```

## Service Registration

```csharp
private static void ConfigureServices(HostBuilderContext context, IServiceCollection services)
{
    // Core services
    services.AddSingleton<IAppEventBus, AppEventBus>();
    services.AddSingleton<IConfigurationService, ConfigurationService>();
    services.AddSingleton<ILifecycleManager, LifecycleManager>();
    
    // Module services
    services.AddSingleton<IUIService, UIService>();
    services.AddSingleton<IWebEngineService, WebEngineService>();
    services.AddSingleton<IAppManagerService, AppManagerService>();
    services.AddSingleton<ISessionManagerService, SessionManagerService>();
    services.AddSingleton<INotificationService, NotificationService>();
    services.AddSingleton<IHotkeyService, HotkeyService>();
    services.AddSingleton<IUpdaterService, UpdaterService>();
    
    // Data services
    services.AddScoped<IAppRepository, AppRepository>();
    services.AddScoped<ISessionRepository, SessionRepository>();
    
    // Configure SQLite
    services.AddDbContext<SideViewDbContext>(options =>
        options.UseSqlite(context.Configuration.GetConnectionString("Default")));
}
```

## Error Handling Strategy

```mermaid
flowchart TD
    A[Exception Occurs] --> B{Exception Type}
    B -->|Critical| C[Log as Fatal]
    B -->|Expected| D[Log as Warning]
    B -->|Unexpected| E[Log as Error]
    
    C --> F[Notify User]
    D --> G[Handle Gracefully]
    E --> H[Try Recovery]
    
    F --> I[Prepare Shutdown]
    G --> J[Continue Operation]
    H --> K{Recovery Success?}
    
    K -->|Yes| J
    K -->|No| L[Escalate to Critical]
    L --> C
    
    I --> M[Save State]
    M --> N[Cleanup Resources]
    N --> O[Exit Application]
```

## Key Events

```csharp
public abstract record AppEvent : IAppEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime Timestamp { get; init; } = DateTime.UtcNow();
}

// Core events
public record ApplicationStartedEvent : AppEvent;
public record ApplicationStoppingEvent : AppEvent;
public record ConfigurationChangedEvent(AppSettings OldSettings, AppSettings NewSettings) : AppEvent;
public record ModuleStartedEvent(string ModuleName) : AppEvent;
public record ModuleStoppedEvent(string ModuleName) : AppEvent;
public record ErrorOccurredEvent(Exception Exception, string Context) : AppEvent;
```

## Performance Considerations

1. **Memory Management**: Use object pooling for frequent allocations
2. **Threading**: All services are thread-safe with minimal locking
3. **Event Processing**: Bounded channels prevent memory leaks
4. **Configuration**: File watcher with debouncing prevents excessive reloads
5. **Logging**: Structured logging with configurable levels

## Testing Strategy

```csharp
[Fact]
public async Task EventBus_PublishAndSubscribe_EventDelivered()
{
    // Arrange
    var logger = Mock.Of<ILogger<AppEventBus>>();
    var eventBus = new AppEventBus(logger);
    var receivedEvent = false;
    
    eventBus.Subscribe<TestEvent>(async (evt, ct) =>
    {
        receivedEvent = true;
        await Task.CompletedTask;
    });
    
    // Act
    await eventBus.PublishAsync(new TestEvent());
    await Task.Delay(100); // Allow processing
    
    // Assert
    Assert.True(receivedEvent);
}
```

## Dependencies

- `Microsoft.Extensions.Hosting` - Generic Host
- `Microsoft.Extensions.DependencyInjection` - DI Container
- `Microsoft.Extensions.Configuration` - Configuration
- `Microsoft.Extensions.Logging` - Logging
- `System.Threading.Channels` - Event Bus
- `System.Text.Json` - Configuration serialization 