/**
 * Activation Engine - Handles panel activation via mouse hover and hotkeys
 * Migrated from C# SideView.UI.Services.ActivationEngine
 */

import { EventEmitter } from 'events';
import { screen, globalShortcut, BrowserWindow } from 'electron';
import { ConfigurationService } from '@modules/core/configuration-service';
import { PanelPosition } from '@shared/types/app.types';
import { ActivationSource, ActivationEventArgs } from '@shared/types/events.types';

export class ActivationEngine extends EventEmitter {
  private readonly configurationService: ConfigurationService;
  private readonly logger: Console;

  private isStarted = false;
  private mouseCheckInterval?: NodeJS.Timeout;
  private registeredHotkeys: string[] = [];
  private lastMousePosition = { x: 0, y: 0 };
  private activationTimer?: NodeJS.Timeout;
  private isInActivationZone = false;
  private isPreviewMode = false;
  private isPanelPinned = false;
  private activationIndicator?: BrowserWindow;
  private isMouseOverPanel = false;
  private panelBounds: { x: number; y: number; width: number; height: number } | null = null;
  private lastMouseCheckTime = 0;
  private mouseCheckDebounceMs = 50; // Debounce mouse checks to prevent rapid toggling

  constructor(
    configurationService: ConfigurationService,
    logger: Console = console
  ) {
    super();
    this.configurationService = configurationService;
    this.logger = logger;
  }

  async start(): Promise<void> {
    if (this.isStarted) {
      return;
    }

    try {
      await this.registerHotkeys();
      this.startMouseTracking();

      this.isStarted = true;

    } catch (error) {
      this.logger.error('Failed to start Activation Engine:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.isStarted) {
      return;
    }

    try {
      this.unregisterHotkeys();
      this.stopMouseTracking();
      this.hideActivationIndicator();

      this.isStarted = false;

    } catch (error) {
      this.logger.error('Error stopping Activation Engine:', error);
      throw error;
    }
  }

  private async registerHotkeys(): Promise<void> {
    const settings = this.configurationService.getSettings();

    if (!settings.hotkeys.enabled) {
      return;
    }

    try {
      // Register toggle panel hotkey
      if (settings.hotkeys.togglePanel) {
        const success = globalShortcut.register(settings.hotkeys.togglePanel, () => {
          this.onHotkeyPressed('togglePanel');
        });

        if (success) {
          this.registeredHotkeys.push(settings.hotkeys.togglePanel);
        } else {
          this.logger.warn(`Failed to register hotkey: ${settings.hotkeys.togglePanel}`);
        }
      }

      // Register new tab hotkey
      if (settings.hotkeys.newTab) {
        const success = globalShortcut.register(settings.hotkeys.newTab, () => {
          this.onHotkeyPressed('newTab');
        });

        if (success) {
          this.registeredHotkeys.push(settings.hotkeys.newTab);
        } else {
          this.logger.warn(`Failed to register hotkey: ${settings.hotkeys.newTab}`);
        }
      }

      // Register refresh hotkey
      if (settings.hotkeys.refresh) {
        const success = globalShortcut.register(settings.hotkeys.refresh, () => {
          this.onHotkeyPressed('refresh');
        });

        if (success) {
          this.registeredHotkeys.push(settings.hotkeys.refresh);
        } else {
          this.logger.warn(`Failed to register hotkey: ${settings.hotkeys.refresh}`);
        }
      }

    } catch (error) {
      this.logger.error('Error registering hotkeys:', error);
      throw error;
    }
  }

  private unregisterHotkeys(): void {
    try {
      for (const hotkey of this.registeredHotkeys) {
        globalShortcut.unregister(hotkey);
      }

      this.registeredHotkeys = [];

    } catch (error) {
      this.logger.error('Error unregistering hotkeys:', error);
    }
  }

  private startMouseTracking(): void {
    const settings = this.configurationService.getSettings();

    if (!settings.ui.autoHide) {
      return;
    }

    // Check mouse position every 100ms
    this.mouseCheckInterval = setInterval(() => {
      this.checkMousePosition();
    }, 100);
  }

  private stopMouseTracking(): void {
    if (this.mouseCheckInterval) {
      clearInterval(this.mouseCheckInterval);
      delete (this as any).mouseCheckInterval;
    }

    if (this.activationTimer) {
      clearTimeout(this.activationTimer);
      delete (this as any).activationTimer;
    }

    this.isInActivationZone = false;
  }

  private checkMousePosition(): void {
    try {
      const currentTime = Date.now();
      
      // Debounce mouse checks to prevent rapid toggling
      if (currentTime - this.lastMouseCheckTime < this.mouseCheckDebounceMs) {
        return;
      }
      
      this.lastMouseCheckTime = currentTime;

      const currentPosition = screen.getCursorScreenPoint();

      // Only check if mouse has moved significantly (reduce noise)
      const mouseMoveThreshold = 2; // pixels
      const xDiff = Math.abs(currentPosition.x - this.lastMousePosition.x);
      const yDiff = Math.abs(currentPosition.y - this.lastMousePosition.y);
      
      if (xDiff < mouseMoveThreshold && yDiff < mouseMoveThreshold) {
        return;
      }

      this.lastMousePosition = currentPosition;

      const settings = this.configurationService.getSettings();
      const display = screen.getPrimaryDisplay();
      const workArea = display.workArea;

      const isInActivationZone = this.isMouseInActivationZone(currentPosition, workArea, settings.ui.panelPosition);

      // Check if mouse is over panel (if panel bounds are available)
      let isOverPanel = false;
      if (this.panelBounds) {
        isOverPanel = currentPosition.x >= this.panelBounds.x &&
                     currentPosition.x <= this.panelBounds.x + this.panelBounds.width &&
                     currentPosition.y >= this.panelBounds.y &&
                     currentPosition.y <= this.panelBounds.y + this.panelBounds.height;
      }

      // Update panel hover state
      if (isOverPanel !== this.isMouseOverPanel) {
        this.isMouseOverPanel = isOverPanel;

        // **IMMEDIATE RESPONSIVENESS: Hide panel immediately when mouse leaves, regardless of other conditions**
        if (!isOverPanel && this.isPreviewMode) {
          this.isPreviewMode = false;
          this.emit('deactivate', new ActivationEventArgs(ActivationSource.MouseHover, { preview: true }));
        }
      }

      // Handle activation zone changes
      if (isInActivationZone && !this.isInActivationZone) {
        this.onMouseEnterActivationZone();
      } else if (!isInActivationZone && this.isInActivationZone) {
        this.onMouseLeaveActivationZone();
      }

    } catch (error) {
      this.logger.error('Error checking mouse position:', error);
    }
  }

  private isMouseInActivationZone(
    mousePos: { x: number; y: number },
    workArea: { x: number; y: number; width: number; height: number },
    panelPosition: PanelPosition
  ): boolean {
    const settings = this.configurationService.getSettings();
    const edgeConfig = settings.ui.edgeActivation;

    // Fallback to legacy behavior if edgeActivation is not configured
    if (!edgeConfig) {
      const activationZoneWidth = 4; // 4 pixel activation zone
      return mousePos.x <= (workArea.x + activationZoneWidth) &&
             mousePos.y >= workArea.y &&
             mousePos.y <= (workArea.y + workArea.height);
    }

    // Determine which edge to check based on panel position
    let activeEdge = edgeConfig.left;
    if (panelPosition === PanelPosition.Right) {
      activeEdge = edgeConfig.right;
    }

    // If the edge is not configured or not enabled, fallback to legacy behavior
    if (!activeEdge || !activeEdge.enabled) {
      const activationZoneWidth = 4; // 4 pixel activation zone
      return mousePos.x <= (workArea.x + activationZoneWidth) &&
             mousePos.y >= workArea.y &&
             mousePos.y <= (workArea.y + workArea.height);
    }

    // Calculate activation zone bounds
    const zoneWidth = activeEdge.width;
    const zoneSize = activeEdge.size; // Percentage of screen edge
    const zoneOffset = activeEdge.offset;

    let zoneBounds: { x: number; y: number; width: number; height: number };

    if (panelPosition === PanelPosition.Left) {
      // Left edge activation
      const zoneHeight = Math.floor((workArea.height * zoneSize) / 100);
      let zoneY = workArea.y;

      switch (activeEdge.position) {
        case 'top':
          zoneY = workArea.y + zoneOffset;
          break;
        case 'middle':
          zoneY = workArea.y + Math.floor((workArea.height - zoneHeight) / 2) + zoneOffset;
          break;
        case 'bottom':
          zoneY = workArea.y + workArea.height - zoneHeight - zoneOffset;
          break;
        case 'full':
          zoneY = workArea.y;
          break;
      }

      zoneBounds = {
        x: workArea.x,
        y: zoneY,
        width: zoneWidth,
        height: activeEdge.position === 'full' ? workArea.height : zoneHeight
      };
    } else {
      // Right edge activation
      const zoneHeight = Math.floor((workArea.height * zoneSize) / 100);
      let zoneY = workArea.y;

      switch (activeEdge.position) {
        case 'top':
          zoneY = workArea.y + zoneOffset;
          break;
        case 'middle':
          zoneY = workArea.y + Math.floor((workArea.height - zoneHeight) / 2) + zoneOffset;
          break;
        case 'bottom':
          zoneY = workArea.y + workArea.height - zoneHeight - zoneOffset;
          break;
        case 'full':
          zoneY = workArea.y;
          break;
      }

      zoneBounds = {
        x: workArea.x + workArea.width - zoneWidth,
        y: zoneY,
        width: zoneWidth,
        height: activeEdge.position === 'full' ? workArea.height : zoneHeight
      };
    }

    // Check if mouse is within the activation zone
    return mousePos.x >= zoneBounds.x &&
           mousePos.x <= zoneBounds.x + zoneBounds.width &&
           mousePos.y >= zoneBounds.y &&
           mousePos.y <= zoneBounds.y + zoneBounds.height;
  }

  private onMouseEnterActivationZone(): void {
    this.isInActivationZone = true;

    const settings = this.configurationService.getSettings();
    const delay = settings.ui.activationDelay;

    // Show visual indicator
    this.showActivationIndicator();

    // Clear any existing timer
    if (this.activationTimer) {
      clearTimeout(this.activationTimer);
    }

    // Set preview timer (shorter delay for preview)
    this.activationTimer = setTimeout(() => {
      if (this.isInActivationZone && !this.isPanelPinned) {
        this.isPreviewMode = true;
        this.emit('activate', new ActivationEventArgs(ActivationSource.MouseHover, { preview: true }));
      }
    }, Math.min(delay, 200)); // Max 200ms for preview
  }

  private onMouseLeaveActivationZone(): void {
    this.isInActivationZone = false;

    // Hide visual indicator
    this.hideActivationIndicator();

    // Clear activation timer
    if (this.activationTimer) {
      clearTimeout(this.activationTimer);
      delete (this as any).activationTimer;
    }

    // **IMMEDIATE RESPONSIVENESS: Hide if in preview mode and not over panel**
    if (this.isPreviewMode && !this.isMouseOverPanel) {
      this.isPreviewMode = false;
      this.emit('deactivate', new ActivationEventArgs(ActivationSource.MouseHover, { preview: true }));
    }
  }

  private onHotkeyPressed(action: string): void {
    switch (action) {
      case 'togglePanel':
        this.emit('activate', new ActivationEventArgs(ActivationSource.Hotkey, { action: 'toggle' }));
        break;

      case 'newTab':
        this.emit('activate', new ActivationEventArgs(ActivationSource.Hotkey, { action: 'newTab' }));
        break;

      case 'refresh':
        this.emit('activate', new ActivationEventArgs(ActivationSource.Hotkey, { action: 'refresh' }));
        break;

      default:
        this.logger.warn(`Unknown hotkey action: ${action}`);
    }
  }

  // Public methods for panel state management
  setPanelPinned(pinned: boolean): void {
    this.isPanelPinned = pinned;
    this.isPreviewMode = false;
  }

  setPanelBounds(bounds: { x: number; y: number; width: number; height: number }): void {
    this.panelBounds = bounds;
  }

  activatePanel(): void {
    if (this.isInActivationZone) {
      this.isPanelPinned = true;
      this.isPreviewMode = false;
      this.emit('activate', new ActivationEventArgs(ActivationSource.MouseClick, { pinned: true }));
    }
  }

  // **NEW: Public getters for mouse state (for grace period logic)**
  getIsMouseOverPanel(): boolean {
    return this.isMouseOverPanel;
  }

  getIsInActivationZone(): boolean {
    return this.isInActivationZone;
  }

  getIsPreviewMode(): boolean {
    return this.isPreviewMode;
  }

  /**
   * Check if panel should be hidden based on current mouse state
   * Used by grace period logic to determine if panel should hide when grace period ends
   */
  shouldHidePanel(): boolean {
    return this.isPreviewMode && !this.isPanelPinned && !this.isMouseOverPanel && !this.isInActivationZone;
  }

  private showActivationIndicator(): void {
    if (this.activationIndicator) {
      return; // Already showing
    }

    const display = screen.getPrimaryDisplay();
    const workArea = display.workArea;
    const settings = this.configurationService.getSettings();
    const edgeConfig = settings.ui.edgeActivation;

    // Fallback to legacy behavior if edgeActivation is not configured
    if (!edgeConfig) {
      this.activationIndicator = new BrowserWindow({
        x: workArea.x,
        y: workArea.y,
        width: 4,
        height: workArea.height,
        frame: false,
        transparent: true,
        alwaysOnTop: true,
        skipTaskbar: true,
        resizable: false,
        movable: false,
        minimizable: false,
        maximizable: false,
        show: false,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true
        }
      });

      this.activationIndicator.loadURL(`data:text/html,
        <html>
          <body style="margin:0;padding:0;background:linear-gradient(90deg, #0078d4 0%, transparent 100%);cursor:pointer;"></body>
        </html>
      `);

      this.activationIndicator.show();
      return;
    }

    // Determine which edge to show based on panel position
    let activeEdge = edgeConfig.left;
    if (settings.ui.panelPosition === PanelPosition.Right) {
      activeEdge = edgeConfig.right;
    }

    if (!activeEdge || !activeEdge.enabled) {
      return; // Don't show indicator if edge is disabled or not configured
    }

    // Calculate indicator bounds
    const zoneWidth = activeEdge.width;
    const zoneSize = activeEdge.size;
    const zoneOffset = activeEdge.offset;
    const zoneHeight = activeEdge.position === 'full' ? workArea.height : Math.floor((workArea.height * zoneSize) / 100);

    let indicatorBounds: { x: number; y: number; width: number; height: number };

    if (settings.ui.panelPosition === PanelPosition.Left) {
      let zoneY = workArea.y;

      switch (activeEdge.position) {
        case 'top':
          zoneY = workArea.y + zoneOffset;
          break;
        case 'middle':
          zoneY = workArea.y + Math.floor((workArea.height - zoneHeight) / 2) + zoneOffset;
          break;
        case 'bottom':
          zoneY = workArea.y + workArea.height - zoneHeight - zoneOffset;
          break;
        case 'full':
          zoneY = workArea.y;
          break;
      }

      indicatorBounds = {
        x: workArea.x,
        y: zoneY,
        width: zoneWidth,
        height: zoneHeight
      };
    } else {
      let zoneY = workArea.y;

      switch (activeEdge.position) {
        case 'top':
          zoneY = workArea.y + zoneOffset;
          break;
        case 'middle':
          zoneY = workArea.y + Math.floor((workArea.height - zoneHeight) / 2) + zoneOffset;
          break;
        case 'bottom':
          zoneY = workArea.y + workArea.height - zoneHeight - zoneOffset;
          break;
        case 'full':
          zoneY = workArea.y;
          break;
      }

      indicatorBounds = {
        x: workArea.x + workArea.width - zoneWidth,
        y: zoneY,
        width: zoneWidth,
        height: zoneHeight
      };
    }

    this.activationIndicator = new BrowserWindow({
      x: indicatorBounds.x,
      y: indicatorBounds.y,
      width: indicatorBounds.width,
      height: indicatorBounds.height,
      frame: false,
      transparent: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      resizable: false,
      movable: false,
      minimizable: false,
      maximizable: false,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true
      }
    });

    // Load simple HTML with colored background and click detection
    const gradient = settings.ui.panelPosition === PanelPosition.Left
      ? 'linear-gradient(90deg, #0078d4 0%, transparent 100%)'
      : 'linear-gradient(270deg, #0078d4 0%, transparent 100%)';

    this.activationIndicator.loadURL(`data:text/html,
      <html>
        <body style="margin:0;padding:0;background:${gradient};cursor:pointer;"></body>
        <script>
          document.body.addEventListener('click', () => {
            require('electron').ipcRenderer.send('activation-click');
          });
        </script>
      </html>
    `);

    this.activationIndicator.show();
  }

  private hideActivationIndicator(): void {
    if (this.activationIndicator) {
      this.activationIndicator.close();
      delete (this as any).activationIndicator;
    }
  }
}
