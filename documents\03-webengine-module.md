# SideView.WebEngine Module Implementation

## Overview
The WebEngine module handles all web content rendering using Microsoft Edge WebView2. It manages multiple web app instances, navigation, JavaScript injection, and communication between the web content and the native application.

## Architecture

```mermaid
graph TB
    A[WebEngineService] --> B[WebAppHost]
    B --> C[CoreWebView2]
    B --> D[NavigationManager]
    B --> E[JSBridge]
    
    C --> F[WebView2 Runtime]
    
    D --> G[UrlValidator]
    D --> H[NavigationHistory]
    
    E --> I[HostObject]
    E --> J[MessageHandler]
    
    subgraph "Session Management"
        K[EnvironmentManager]
        L[UserDataManager]
        M[CookieManager]
    end
    
    B --> K
    K --> L
    K --> M
```

## Key Components

### 1. WebApp Host (`WebAppHost.cs`)

```mermaid
stateDiagram-v2
    [*] --> Initializing
    Initializing --> Creating: Create Environment
    Creating --> Loading: Load WebView2
    Loading --> Ready: WebView2 Loaded
    Ready --> Navigating: Navigate to URL
    Navigating --> Loaded: Page Loaded
    Loaded --> Ready: Navigation Complete
    
    Ready --> Disposing: Dispose Called
    Loaded --> Disposing: Dispose Called
    Disposing --> [*]
    
    note right of Ready
        WebView2 is ready
        for navigation and
        interaction
    end note
```

```csharp
public class WebAppHost : UserControl, IWebAppHost, IDisposable
{
    private readonly ILogger<WebAppHost> _logger;
    private readonly AppModel _app;
    private readonly ISessionManagerService _sessionManager;
    
    private WebView2 _webView;
    private CoreWebView2Environment _environment;
    private NavigationManager _navigationManager;
    private JSBridge _jsBridge;
    private bool _isInitialized;
    private bool _isDisposed;
    
    public event EventHandler<NavigationEventArgs> NavigationStarted;
    public event EventHandler<NavigationEventArgs> NavigationCompleted;
    public event EventHandler<TitleChangedEventArgs> TitleChanged;
    public event EventHandler<FaviconChangedEventArgs> FaviconChanged;
    
    public WebAppHost(
        AppModel app,
        ISessionManagerService sessionManager,
        ILogger<WebAppHost> logger)
    {
        _app = app;
        _sessionManager = sessionManager;
        _logger = logger;
        
        InitializeComponent();
    }
    
    public async Task InitializeAsync()
    {
        try
        {
            _logger.LogInformation("Initializing WebAppHost for app {AppId}", _app.Id);
            
            // Create environment based on session mode
            _environment = await CreateEnvironmentAsync();
            
            // Initialize WebView2
            _webView = new WebView2();
            Content = _webView;
            
            await _webView.EnsureCoreWebView2Async(_environment);
            
            // Setup components
            _navigationManager = new NavigationManager(_webView.CoreWebView2, _logger);
            _jsBridge = new JSBridge(_webView.CoreWebView2, _logger);
            
            // Setup event handlers
            SetupEventHandlers();
            
            // Configure WebView2
            await ConfigureWebViewAsync();
            
            _isInitialized = true;
            _logger.LogInformation("WebAppHost initialized successfully for app {AppId}", _app.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize WebAppHost for app {AppId}", _app.Id);
            throw;
        }
    }
    
    private async Task<CoreWebView2Environment> CreateEnvironmentAsync()
    {
        var userDataFolder = _sessionManager.GetUserDataFolder(_app);
        
        var options = CoreWebView2EnvironmentOptions.CreateDefault();
        options.AdditionalBrowserArguments = GetBrowserArguments();
        
        return await CoreWebView2Environment.CreateAsync(
            browserExecutableFolder: null,
            userDataFolder: userDataFolder,
            options: options);
    }
    
    private string GetBrowserArguments()
    {
        var args = new List<string>
        {
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-features=TranslateUI"
        };
        
        if (_app.SessionMode == SessionMode.Isolated)
        {
            args.Add("--site-per-process");
        }
        
        return string.Join(" ", args);
    }
}
```

### 2. Navigation Manager (`NavigationManager.cs`)

```mermaid
sequenceDiagram
    participant H as WebAppHost
    participant N as NavigationManager
    participant W as WebView2
    participant V as UrlValidator
    participant S as SecurityChecker
    
    H->>N: NavigateAsync(url)
    N->>V: ValidateUrl(url)
    V->>N: Valid/Invalid
    alt URL is valid
        N->>S: CheckSecurity(url)
        S->>N: Safe/Unsafe
        alt URL is safe
            N->>W: Navigate(url)
            W->>N: NavigationStarted
            N->>H: OnNavigationStarted
            W->>N: NavigationCompleted
            N->>H: OnNavigationCompleted
        else URL is unsafe
            N->>H: OnNavigationBlocked
        end
    else URL is invalid
        N->>H: OnNavigationError
    end
```

```csharp
public class NavigationManager
{
    private readonly CoreWebView2 _webView;
    private readonly ILogger _logger;
    private readonly UrlValidator _urlValidator;
    private readonly SecurityChecker _securityChecker;
    private readonly NavigationHistory _history;
    
    public event EventHandler<NavigationEventArgs> NavigationStarted;
    public event EventHandler<NavigationEventArgs> NavigationCompleted;
    public event EventHandler<NavigationEventArgs> NavigationBlocked;
    
    public NavigationManager(CoreWebView2 webView, ILogger logger)
    {
        _webView = webView;
        _logger = logger;
        _urlValidator = new UrlValidator();
        _securityChecker = new SecurityChecker();
        _history = new NavigationHistory();
        
        SetupEventHandlers();
    }
    
    public async Task<bool> NavigateAsync(string url)
    {
        try
        {
            _logger.LogInformation("Attempting navigation to {Url}", url);
            
            // Validate URL
            if (!_urlValidator.IsValid(url))
            {
                _logger.LogWarning("Invalid URL blocked: {Url}", url);
                return false;
            }
            
            // Security check
            var securityResult = await _securityChecker.CheckAsync(url);
            if (!securityResult.IsSafe)
            {
                _logger.LogWarning("Unsafe URL blocked: {Url}, Reason: {Reason}", 
                    url, securityResult.Reason);
                NavigationBlocked?.Invoke(this, new NavigationEventArgs(url, securityResult.Reason));
                return false;
            }
            
            // Perform navigation
            _webView.Navigate(url);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Navigation failed for URL: {Url}", url);
            return false;
        }
    }
    
    private void SetupEventHandlers()
    {
        _webView.NavigationStarting += OnNavigationStarting;
        _webView.NavigationCompleted += OnNavigationCompleted;
        _webView.DOMContentLoaded += OnDOMContentLoaded;
    }
    
    private void OnNavigationStarting(object sender, CoreWebView2NavigationStartingEventArgs e)
    {
        _logger.LogDebug("Navigation starting: {Uri}", e.Uri);
        
        // Additional security check for redirects
        if (!_urlValidator.IsValid(e.Uri))
        {
            e.Cancel = true;
            _logger.LogWarning("Navigation cancelled due to invalid URL: {Uri}", e.Uri);
            return;
        }
        
        _history.AddEntry(e.Uri);
        NavigationStarted?.Invoke(this, new NavigationEventArgs(e.Uri));
    }
    
    private void OnNavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        var url = _webView.Source;
        _logger.LogDebug("Navigation completed: {Uri}, Success: {Success}", url, e.IsSuccess);
        
        if (e.IsSuccess)
        {
            NavigationCompleted?.Invoke(this, new NavigationEventArgs(url));
        }
        else
        {
            _logger.LogWarning("Navigation failed: {Uri}, WebErrorStatus: {Error}", 
                url, e.WebErrorStatus);
        }
    }
}
```

### 3. JavaScript Bridge (`JSBridge.cs`)

```mermaid
graph LR
    A[Native App] --> B[JSBridge]
    B --> C[HostObject]
    C --> D[WebView2]
    D --> E[Web Content]
    
    E --> F[JavaScript API]
    F --> G[PostMessage]
    G --> D
    D --> H[MessageReceived]
    H --> B
    B --> I[Event Handler]
    I --> A
    
    subgraph "Bidirectional Communication"
        J[Native → Web]
        K[Web → Native]
    end
    
    B --> J
    B --> K
```

```csharp
public class JSBridge
{
    private readonly CoreWebView2 _webView;
    private readonly ILogger _logger;
    private readonly ConcurrentDictionary<string, Func<string, Task<string>>> _handlers;
    
    public JSBridge(CoreWebView2 webView, ILogger logger)
    {
        _webView = webView;
        _logger = logger;
        _handlers = new ConcurrentDictionary<string, Func<string, Task<string>>>();
        
        SetupBridge();
    }
    
    private void SetupBridge()
    {
        // Add host object for native API exposure
        var hostObject = new SideViewHostObject(this);
        _webView.AddHostObjectToScript("sideView", hostObject);
        
        // Handle messages from web content
        _webView.WebMessageReceived += OnWebMessageReceived;
        
        // Inject initialization script
        InjectInitializationScript();
    }
    
    private void InjectInitializationScript()
    {
        var script = @"
            (function() {
                if (window.sideViewAPI) return; // Already initialized
                
                window.sideViewAPI = {
                    // Notification API
                    showNotification: function(title, options) {
                        return window.chrome.webview.hostObjects.sideView.ShowNotification(
                            JSON.stringify({title, ...options})
                        );
                    },
                    
                    // Storage API
                    setStorage: function(key, value) {
                        return window.chrome.webview.hostObjects.sideView.SetStorage(key, value);
                    },
                    
                    getStorage: function(key) {
                        return window.chrome.webview.hostObjects.sideView.GetStorage(key);
                    },
                    
                    // Theme API
                    getTheme: function() {
                        return window.chrome.webview.hostObjects.sideView.GetTheme();
                    },
                    
                    onThemeChanged: function(callback) {
                        window.addEventListener('sideview-theme-changed', callback);
                    }
                };
                
                // Enhance native notifications
                if (window.Notification) {
                    const originalNotification = window.Notification;
                    window.Notification = function(title, options) {
                        window.sideViewAPI.showNotification(title, options);
                        return new originalNotification(title, options);
                    };
                    Object.setPrototypeOf(window.Notification, originalNotification);
                }
                
                console.log('SideView API initialized');
            })();
        ";
        
        _webView.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.Document);
        _webView.WebResourceRequested += (s, e) =>
        {
            // Inject script for all page loads
            _ = _webView.AddScriptToExecuteOnDocumentCreatedAsync(script);
        };
    }
    
    public void RegisterHandler(string command, Func<string, Task<string>> handler)
    {
        _handlers[command] = handler;
        _logger.LogDebug("Registered JS bridge handler: {Command}", command);
    }
    
    private async void OnWebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
    {
        try
        {
            var message = e.TryGetWebMessageAsString();
            var request = JsonSerializer.Deserialize<JsBridgeRequest>(message);
            
            if (_handlers.TryGetValue(request.Command, out var handler))
            {
                var response = await handler(request.Data);
                await SendResponseAsync(request.Id, response);
            }
            else
            {
                _logger.LogWarning("Unknown JS bridge command: {Command}", request.Command);
                await SendErrorAsync(request.Id, "Unknown command");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing web message");
        }
    }
    
    private async Task SendResponseAsync(string requestId, string data)
    {
        var response = new JsBridgeResponse
        {
            Id = requestId,
            Success = true,
            Data = data
        };
        
        var json = JsonSerializer.Serialize(response);
        await _webView.PostWebMessageAsStringAsync(json);
    }
}

[ComVisible(true)]
public class SideViewHostObject
{
    private readonly JSBridge _bridge;
    
    public SideViewHostObject(JSBridge bridge)
    {
        _bridge = bridge;
    }
    
    public string ShowNotification(string data)
    {
        // Handle notification request
        return "OK";
    }
    
    public string SetStorage(string key, string value)
    {
        // Handle storage set request
        return "OK";
    }
    
    public string GetStorage(string key)
    {
        // Handle storage get request
        return "value";
    }
    
    public string GetTheme()
    {
        // Return current theme
        return JsonSerializer.Serialize(new { theme = "dark", accent = "#0078D4" });
    }
}
```

### 4. Environment Management (`EnvironmentManager.cs`)

```mermaid
flowchart TD
    A[Create Environment] --> B{Session Mode}
    B -->|Shared| C[Use Shared Profile]
    B -->|Isolated| D[Create Isolated Profile]
    
    C --> E[%LOCALAPPDATA%/SideView/Shared]
    D --> F[%LOCALAPPDATA%/SideView/Apps/{AppId}]
    
    E --> G[Configure Options]
    F --> G
    
    G --> H[Set Browser Args]
    H --> I[Apply Security Settings]
    I --> J[Create WebView2 Environment]
    
    subgraph "Environment Options"
        K[Language Settings]
        L[Download Behavior]
        M[Permission Policies]
        N[Custom Schemes]
    end
    
    G --> K
    G --> L
    G --> M
    G --> N
```

```csharp
public class EnvironmentManager
{
    private readonly ILogger<EnvironmentManager> _logger;
    private readonly IConfigurationService _config;
    private readonly ConcurrentDictionary<string, CoreWebView2Environment> _environments;
    
    public EnvironmentManager(
        ILogger<EnvironmentManager> logger,
        IConfigurationService config)
    {
        _logger = logger;
        _config = config;
        _environments = new ConcurrentDictionary<string, CoreWebView2Environment>();
    }
    
    public async Task<CoreWebView2Environment> GetEnvironmentAsync(AppModel app)
    {
        var key = GetEnvironmentKey(app);
        
        if (_environments.TryGetValue(key, out var existingEnv))
        {
            return existingEnv;
        }
        
        var environment = await CreateEnvironmentAsync(app);
        _environments[key] = environment;
        
        return environment;
    }
    
    private async Task<CoreWebView2Environment> CreateEnvironmentAsync(AppModel app)
    {
        var userDataFolder = GetUserDataFolder(app);
        var options = CreateEnvironmentOptions(app);
        
        _logger.LogInformation("Creating WebView2 environment for app {AppId} at {Path}", 
            app.Id, userDataFolder);
        
        var environment = await CoreWebView2Environment.CreateAsync(
            browserExecutableFolder: null,
            userDataFolder: userDataFolder,
            options: options);
            
        // Configure environment
        await ConfigureEnvironmentAsync(environment, app);
        
        return environment;
    }
    
    private CoreWebView2EnvironmentOptions CreateEnvironmentOptions(AppModel app)
    {
        var options = CoreWebView2EnvironmentOptions.CreateDefault();
        
        // Language and locale
        options.Language = _config.Settings.Language ?? "en-US";
        
        // Additional browser arguments
        var args = new List<string>
        {
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-dev-shm-usage" // Prevent /dev/shm issues in containers
        };
        
        if (app.SessionMode == SessionMode.Isolated)
        {
            args.Add("--site-per-process");
            args.Add("--process-per-tab");
        }
        
        if (_config.Settings.DisableWebSecurity)
        {
            args.Add("--disable-web-security");
            args.Add("--disable-site-isolation-trials");
        }
        
        options.AdditionalBrowserArguments = string.Join(" ", args);
        
        return options;
    }
    
    private async Task ConfigureEnvironmentAsync(CoreWebView2Environment environment, AppModel app)
    {
        // Set up custom schemes if needed
        var customSchemes = new[]
        {
            "sideview-asset",
            "sideview-api"
        };
        
        foreach (var scheme in customSchemes)
        {
            environment.CreateCustomSchemeRegistration(scheme, new CoreWebView2CustomSchemeRegistration
            {
                IsStandard = true,
                IsLocal = false,
                IsDisplayIsolated = false,
                IsSecure = true,
                IsCorsEnabled = true,
                HasFetchHandler = true
            });
        }
    }
    
    private string GetUserDataFolder(AppModel app)
    {
        var baseFolder = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
            "SideView");
            
        return app.SessionMode switch
        {
            SessionMode.Shared => Path.Combine(baseFolder, "Shared"),
            SessionMode.Isolated => Path.Combine(baseFolder, "Apps", app.Id.ToString()),
            _ => throw new ArgumentOutOfRangeException()
        };
    }
    
    private string GetEnvironmentKey(AppModel app)
    {
        return app.SessionMode == SessionMode.Shared 
            ? "shared" 
            : $"app-{app.Id}";
    }
}
```

## Security Implementation

```mermaid
flowchart TD
    A[Web Request] --> B[Security Filter]
    B --> C{Request Type}
    C -->|Navigation| D[URL Security Check]
    C -->|Resource| E[Resource Security Check]
    C -->|Script| F[Script Security Check]
    
    D --> G{URL Safe?}
    E --> H{Resource Safe?}
    F --> I{Script Safe?}
    
    G -->|Yes| J[Allow Navigation]
    G -->|No| K[Block & Log]
    
    H -->|Yes| L[Allow Resource]
    H -->|No| K
    
    I -->|Yes| M[Allow Script]
    I -->|No| K
    
    subgraph "Security Policies"
        N[CSP Headers]
        O[HTTPS Enforcement]
        P[Domain Whitelist]
        Q[Script Injection Prevention]
    end
    
    B --> N
    B --> O
    B --> P
    B --> Q
```

## Performance Monitoring

```csharp
public class WebEnginePerformanceMonitor
{
    private readonly ILogger<WebEnginePerformanceMonitor> _logger;
    private readonly Timer _monitoringTimer;
    private readonly ConcurrentDictionary<string, PerformanceMetrics> _metrics;
    
    public void StartMonitoring(CoreWebView2 webView, string appId)
    {
        var metrics = new PerformanceMetrics();
        _metrics[appId] = metrics;
        
        // Monitor memory usage
        webView.ProcessFailed += (s, e) =>
        {
            _logger.LogError("WebView2 process failed for app {AppId}: {Reason}", 
                appId, e.Reason);
            metrics.ProcessFailures++;
        };
        
        // Monitor performance
        _ = Task.Run(async () =>
        {
            while (!metrics.IsDisposed)
            {
                try
                {
                    var memoryUsage = await GetMemoryUsageAsync(webView);
                    metrics.UpdateMemoryUsage(memoryUsage);
                    
                    if (memoryUsage > 500 * 1024 * 1024) // 500MB threshold
                    {
                        _logger.LogWarning("High memory usage detected for app {AppId}: {Usage}MB", 
                            appId, memoryUsage / 1024 / 1024);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error monitoring performance for app {AppId}", appId);
                }
                
                await Task.Delay(TimeSpan.FromSeconds(30));
            }
        });
    }
    
    private async Task<long> GetMemoryUsageAsync(CoreWebView2 webView)
    {
        // Use WebView2 APIs to get memory information
        var info = await webView.CallDevToolsProtocolMethodAsync(
            "Runtime.getHeapUsage", "{}");
            
        var result = JsonSerializer.Deserialize<HeapUsageResult>(info);
        return result.usedSize + result.totalSize;
    }
}
```

## Testing Framework

```csharp
[TestClass]
public class WebAppHostTests
{
    private TestWebAppHost _host;
    private MockSessionManager _sessionManager;
    
    [TestInitialize]
    public async Task Setup()
    {
        _sessionManager = new MockSessionManager();
        _host = new TestWebAppHost(CreateTestApp(), _sessionManager);
        await _host.InitializeAsync();
    }
    
    [TestMethod]
    public async Task NavigateAsync_ValidUrl_NavigatesSuccessfully()
    {
        // Arrange
        var url = "https://example.com";
        var navigationCompleted = false;
        
        _host.NavigationCompleted += (s, e) => navigationCompleted = true;
        
        // Act
        var result = await _host.NavigateAsync(url);
        await Task.Delay(2000); // Wait for navigation
        
        // Assert
        Assert.IsTrue(result);
        Assert.IsTrue(navigationCompleted);
        Assert.AreEqual(url, _host.CurrentUrl);
    }
    
    [TestMethod]
    public async Task JSBridge_MessageFromWeb_HandledCorrectly()
    {
        // Arrange
        var receivedMessage = string.Empty;
        _host.RegisterJSHandler("test", (data) =>
        {
            receivedMessage = data;
            return Task.FromResult("response");
        });
        
        // Act
        await _host.ExecuteScriptAsync(@"
            window.chrome.webview.postMessage(JSON.stringify({
                command: 'test',
                data: 'hello from web'
            }));
        ");
        
        await Task.Delay(100);
        
        // Assert
        Assert.AreEqual("hello from web", receivedMessage);
    }
}
```

## Dependencies

- `Microsoft.Web.WebView2` - WebView2 runtime
- `Microsoft.Extensions.Logging` - Logging
- `System.Text.Json` - JSON serialization
- `System.Runtime.InteropServices` - COM interop 