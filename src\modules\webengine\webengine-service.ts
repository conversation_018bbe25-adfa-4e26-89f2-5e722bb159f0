/**
 * WebEngine Service - Web content management
 * Migrated from C# SideView.WebEngine.Services.WebEngineService
 */

import { ipcMain } from 'electron';
import { WebAppModel } from '@shared/types/app.types';
import { AppEventBus } from '@modules/core/event-bus';
import { ConfigurationService } from '@modules/core/configuration-service';
import { SessionManagerService } from '@modules/session/session-service';
import {
  WebEngineModuleStartedEvent,
  WebAppActivatedEvent,
  WebAppNavigationCompletedEvent,
  NavigationEventArgs,
  TitleChangedEventArgs
} from '@shared/types/events.types';
import { WebAppHost } from './web-app-host';

export class WebEngineService {
  private readonly eventBus: AppEventBus;
  private readonly configurationService: ConfigurationService;
  private readonly sessionManagerService: SessionManagerService;
  private readonly logger: Console;
  private readonly webAppHosts = new Map<string, WebAppHost>();
  private activeWebAppHost?: WebAppHost;
  private isInitialized = false;

  constructor(
    eventBus: AppEventBus,
    configurationService: ConfigurationService,
    sessionManagerService: SessionManagerService,
    logger: Console = console
  ) {
    this.eventBus = eventBus;
    this.configurationService = configurationService;
    this.sessionManagerService = sessionManagerService;
    this.logger = logger;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      this.setupIpcHandlers();
      this.isInitialized = true;

    } catch (error) {
      this.logger.error('Failed to initialize WebEngine Module:', error);
      throw error;
    }
  }

  async start(): Promise<void> {
    await this.eventBus.publish(new WebEngineModuleStartedEvent());
  }

  async stop(): Promise<void> {
    this.logger.info('Stopping WebEngine Module');

    // Clean up all web app hosts
    for (const [appId, host] of this.webAppHosts) {
      try {
        await host.dispose();
      } catch (error) {
        this.logger.error(`Error disposing WebAppHost for ${appId}:`, error);
      }
    }

    this.webAppHosts.clear();
    delete (this as any).activeWebAppHost;

    this.logger.info('WebEngine Module stopped successfully');
  }

  async createWebAppHost(app: WebAppModel): Promise<WebAppHost> {
    this.logger.info(`Creating WebAppHost for app ${app.id}: ${app.name}`);

    let webAppHost: WebAppHost | undefined;

    try {
      // Get session for the app
      const session = await this.sessionManagerService.getSessionForApp(app);

      // Create web app host
      webAppHost = new WebAppHost(
        app,
        session,
        this.configurationService,
        this.logger
      );

      // Store the host immediately to prevent race conditions
      this.webAppHosts.set(app.id, webAppHost);

      // Setup event handlers
      webAppHost.on('navigation-completed', (args: NavigationEventArgs) => {
        this.onWebAppNavigationCompleted(app.id, args);
      });

      webAppHost.on('title-changed', (args: TitleChangedEventArgs) => {
        this.onWebAppTitleChanged(app.id, args);
      });

      webAppHost.on('loading-state-changed', (isLoading: boolean) => {
        this.onWebAppLoadingStateChanged(app.id, isLoading);
      });

      // Initialize the host
      await webAppHost.initialize();

      this.logger.info(`WebAppHost initialized successfully for app ${app.id}`);

      // Navigate to the app URL (but don't fail WebAppHost creation if navigation fails)
      if (app.url) {
        try {
          await webAppHost.navigate(app.url);
          this.logger.info(`WebAppHost navigation completed for app ${app.id}`);
        } catch (navigationError) {
          this.logger.warn(`Navigation failed for app ${app.id}, but WebAppHost will remain available:`, navigationError);
          // Don't throw here - WebAppHost should remain available even if initial navigation fails
        }
      }

      this.logger.info(`WebAppHost created successfully for app ${app.id}`);
      return webAppHost;

    } catch (error) {
      // Only remove from map if the WebAppHost creation or initialization failed
      // Don't remove for navigation failures
      if (webAppHost && this.webAppHosts.get(app.id) === webAppHost) {
        // Check if the error was during initialization (before navigation)
        if (!webAppHost.IsInitialized) {
          this.webAppHosts.delete(app.id);
          this.logger.error(`Failed to create/initialize WebAppHost for app ${app.id}, removing from map:`, error);
        } else {
          this.logger.warn(`WebAppHost for app ${app.id} created but navigation failed, keeping in map:`, error);
        }
      } else {
        this.logger.error(`Failed to create WebAppHost for app ${app.id}:`, error);
      }
      throw error;
    }
  }

  async removeWebAppHost(appId: string): Promise<void> {
    const webAppHost = this.webAppHosts.get(appId);
    if (!webAppHost) {
      this.logger.warn(`WebAppHost for app ${appId} not found`);
      return;
    }

    this.logger.info(`Removing WebAppHost for app ${appId}`);

    try {
      // If this is the active host, clear it
      if (this.activeWebAppHost === webAppHost) {
        delete (this as any).activeWebAppHost;
      }

      // Dispose the host
      await webAppHost.dispose();

      // Remove from map
      this.webAppHosts.delete(appId);

      this.logger.info(`WebAppHost removed successfully for app ${appId}`);

    } catch (error) {
      this.logger.error(`Error removing WebAppHost for app ${appId}:`, error);
      throw error;
    }
  }

  async switchToWebApp(appId: string): Promise<void> {
    console.log(`[SERVICE-LIFECYCLE] WebEngineService.switchToWebApp called: ${appId}`);

    const webAppHost = this.webAppHosts.get(appId);
    if (!webAppHost) {
      console.error(`[SERVICE-LIFECYCLE] ERROR: WebAppHost for ${appId} not found`);
      throw new Error(`WebAppHost for app ${appId} not found`);
    }

    // Check if WebAppHost is initialized
    if (!webAppHost.IsInitialized) {
      console.warn(`[SERVICE-LIFECYCLE] WARNING: WebAppHost for ${appId} not initialized, waiting...`);
      await new Promise(resolve => setTimeout(resolve, 100));

      if (!webAppHost.IsInitialized) {
        console.error(`[SERVICE-LIFECYCLE] ERROR: WebAppHost for ${appId} still not initialized`);
        throw new Error(`WebAppHost for app ${appId} is not initialized`);
      }
    }

    try {
      // Deactivate current host
      if (this.activeWebAppHost && this.activeWebAppHost !== webAppHost) {
        await this.activeWebAppHost.setActive(false);
      }

      // Activate new host
      this.activeWebAppHost = webAppHost;
      await webAppHost.setActive(true);

      // Update app model
      webAppHost.app.isActive = true;
      webAppHost.app.lastAccessed = new Date();

      // Deactivate other apps
      for (const [otherId, otherHost] of this.webAppHosts) {
        if (otherId !== appId) {
          otherHost.app.isActive = false;
        }
      }

      // Publish event
      await this.eventBus.publish(new WebAppActivatedEvent(appId));

    } catch (error) {
      console.error(`Failed to switch to WebApp ${appId}:`, error);
      throw error;
    }
  }

  getActiveWebApp(): WebAppModel | undefined {
    return this.activeWebAppHost?.app;
  }

  getActiveWebAppHost(): WebAppHost | undefined {
    return this.activeWebAppHost;
  }

  getWebAppHost(appId: string): WebAppHost | undefined {
    return this.webAppHosts.get(appId);
  }

  // **CRITICAL FIX 15: Helper method for debugging WebAppHost availability**
  getAllWebAppHosts(): WebAppHost[] {
    return Array.from(this.webAppHosts.values());
  }

  async navigateWebApp(appId: string, url: string): Promise<void> {
    const webAppHost = this.webAppHosts.get(appId);
    if (!webAppHost) {
      throw new Error(`WebAppHost for app ${appId} not found`);
    }

    await webAppHost.navigate(url);
  }

  async reloadWebApp(appId: string): Promise<void> {
    const webAppHost = this.webAppHosts.get(appId);
    if (!webAppHost) {
      throw new Error(`WebAppHost for app ${appId} not found`);
    }

    await webAppHost.reload();
  }

  async executeScript(appId: string, script: string): Promise<any> {
    const webAppHost = this.webAppHosts.get(appId);
    if (!webAppHost) {
      throw new Error(`WebAppHost for app ${appId} not found`);
    }

    return await webAppHost.executeScript(script);
  }

  private setupIpcHandlers(): void {
    // Remove existing handlers first to prevent conflicts
    try {
      ipcMain.removeHandler('webengine:navigate');
      ipcMain.removeHandler('webengine:reload');
      ipcMain.removeHandler('webengine:executeScript');
      ipcMain.removeHandler('webengine:getActiveApp');
    } catch (error) {
      // Handlers may not exist yet, ignore
    }

    // WebView management
    ipcMain.handle('webengine:navigate', async (_, appId: string, url: string) => {
      return this.navigateWebApp(appId, url);
    });

    ipcMain.handle('webengine:reload', async (_, appId: string) => {
      return this.reloadWebApp(appId);
    });

    ipcMain.handle('webengine:executeScript', async (_, appId: string, script: string) => {
      return this.executeScript(appId, script);
    });

    ipcMain.handle('webengine:getActiveApp', async () => {
      return this.getActiveWebApp();
    });

    this.logger.debug('WebEngine IPC handlers registered');
  }

  private async onWebAppNavigationCompleted(appId: string, args: NavigationEventArgs): Promise<void> {
    this.logger.debug(`Navigation completed for app ${appId}: ${args.url}`);

    await this.eventBus.publish(new WebAppNavigationCompletedEvent(appId, args.url, true));
  }

  private async onWebAppTitleChanged(appId: string, args: TitleChangedEventArgs): Promise<void> {
    this.logger.debug(`Title changed for app ${appId}: ${args.title}`);

    // Update app model if needed
    const webAppHost = this.webAppHosts.get(appId);
    if (webAppHost) {
      // Could store title in app model for display purposes
    }
  }

  private async onWebAppLoadingStateChanged(appId: string, isLoading: boolean): Promise<void> {
    this.logger.debug(`Loading state changed for app ${appId}: ${isLoading ? 'loading' : 'loaded'}`);

    // Could emit loading state events for UI updates
  }
}
