/**
 * Theme Manager - Handles dark/light mode and OS theme detection
 * Migrated from C# SideView.UI.Services.ThemeManager
 */

import { EventEmitter } from 'events';
import { nativeTheme, ipcMain } from 'electron';
import { ConfigurationService } from '@modules/core/configuration-service';
import { Theme } from '@shared/types/app.types';

export interface ThemeChangedEventArgs {
  theme: Theme;
  isDark: boolean;
  source: 'user' | 'system';
}

export class ThemeManager extends EventEmitter {
  private readonly configurationService: ConfigurationService;
  private readonly logger: Console;
  
  private currentTheme: Theme = Theme.System;
  private isDarkMode = false;
  private isInitialized = false;

  constructor(
    configurationService: ConfigurationService,
    logger: Console = console
  ) {
    super();
    this.configurationService = configurationService;
    this.logger = logger;
  }

  get CurrentTheme(): Theme {
    return this.currentTheme;
  }

  get IsDarkMode(): boolean {
    return this.isDarkMode;
  }

  getCurrentTheme(): string {
    return this.currentTheme;
  }

  getIsDarkMode(): boolean {
    return this.isDarkMode;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    this.logger.info('Initializing Theme Manager');

    try {
      // Get initial theme from configuration
      const settings = this.configurationService.getSettings();
      this.currentTheme = settings.ui.theme;

      // Setup native theme listeners
      this.setupNativeThemeListeners();

      // Setup IPC handlers
      this.setupIpcHandlers();

      // Apply initial theme
      await this.applyTheme(this.currentTheme);

      // Listen for configuration changes
      this.configurationService.on('configurationChanged', (args) => {
        if (args.changedKeys.includes('ui.theme')) {
          this.onThemeConfigurationChanged(args.newSettings.ui.theme);
        }
      });

      this.isInitialized = true;
      this.logger.info('Theme Manager initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize Theme Manager:', error);
      throw error;
    }
  }

  async setTheme(theme: Theme): Promise<void> {
    if (theme === this.currentTheme) {
      return;
    }

    this.logger.info(`Setting theme to: ${theme}`);

    try {
      // Update configuration
      const currentSettings = this.configurationService.getSettings();
      await this.configurationService.updateSettings({
        ui: {
          ...currentSettings.ui,
          theme
        }
      });

      // Apply theme will be called via configuration change event

    } catch (error) {
      this.logger.error(`Failed to set theme to ${theme}:`, error);
      throw error;
    }
  }

  async applyTheme(theme: Theme): Promise<void> {
    this.logger.debug(`Applying theme: ${theme}`);

    try {
      this.currentTheme = theme;
      
      // Determine if we should use dark mode
      const shouldUseDark = this.shouldUseDarkMode(theme);
      
      if (this.isDarkMode !== shouldUseDark) {
        this.isDarkMode = shouldUseDark;
        
        // Update native theme
        nativeTheme.themeSource = this.getNativeThemeSource(theme);
        
        // Emit theme changed event
        this.emit('theme-changed', {
          theme,
          isDark: this.isDarkMode,
          source: 'user'
        } as ThemeChangedEventArgs);

        this.logger.info(`Theme applied: ${theme} (dark: ${this.isDarkMode})`);
      }

    } catch (error) {
      this.logger.error(`Failed to apply theme ${theme}:`, error);
      throw error;
    }
  }

  getThemeVariables(): Record<string, string> {
    const isDark = this.isDarkMode;
    
    return {
      // Background colors
      '--panel-background': isDark ? '#2d2d30' : '#ffffff',
      '--panel-background-secondary': isDark ? '#3e3e42' : '#f6f6f6',
      '--panel-border': isDark ? '#464647' : '#e1e1e1',
      
      // Text colors
      '--text-primary': isDark ? '#ffffff' : '#000000',
      '--text-secondary': isDark ? '#cccccc' : '#666666',
      '--text-muted': isDark ? '#999999' : '#999999',
      
      // Accent colors
      '--accent-color': '#0078d4',
      '--accent-hover': '#106ebe',
      '--accent-pressed': '#005a9e',
      
      // Interactive elements
      '--button-background': isDark ? '#0e639c' : '#0078d4',
      '--button-hover': isDark ? '#1177bb' : '#106ebe',
      '--button-text': '#ffffff',
      
      // App list
      '--app-item-hover': isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
      '--app-item-active': isDark ? 'rgba(0, 120, 212, 0.3)' : 'rgba(0, 120, 212, 0.1)',
      
      // Scrollbar
      '--scrollbar-track': isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
      '--scrollbar-thumb': isDark ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
      '--scrollbar-thumb-hover': isDark ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)',
      
      // Status colors
      '--success-color': '#107c10',
      '--warning-color': '#ff8c00',
      '--error-color': '#d13438',
      
      // Shadows
      '--shadow-light': isDark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.1)',
      '--shadow-medium': isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.2)',
      '--shadow-heavy': isDark ? 'rgba(0, 0, 0, 0.9)' : 'rgba(0, 0, 0, 0.3)'
    };
  }

  private setupNativeThemeListeners(): void {
    // Listen for system theme changes
    nativeTheme.on('updated', () => {
      this.onNativeThemeUpdated();
    });

    this.logger.debug('Native theme listeners setup');
  }

  private setupIpcHandlers(): void {
    // Remove existing handlers first to prevent conflicts
    try {
      ipcMain.removeHandler('theme:get');
      ipcMain.removeHandler('theme:set');
      ipcMain.removeHandler('theme:getVariables');
    } catch (error) {
      // Handlers may not exist yet, ignore
    }

    // Theme management
    ipcMain.handle('theme:get', () => {
      return {
        theme: this.currentTheme,
        isDark: this.isDarkMode,
        variables: this.getThemeVariables()
      };
    });

    ipcMain.handle('theme:set', async (_, theme: Theme) => {
      return this.setTheme(theme);
    });

    ipcMain.handle('theme:getVariables', () => {
      return this.getThemeVariables();
    });

    this.logger.debug('Theme IPC handlers registered');
  }

  private onThemeConfigurationChanged(newTheme: Theme): void {
    this.logger.debug(`Theme configuration changed to: ${newTheme}`);
    this.applyTheme(newTheme);
  }

  private onNativeThemeUpdated(): void {
    // Only react to system theme changes if we're in system mode
    if (this.currentTheme === Theme.System) {
      const shouldUseDark = nativeTheme.shouldUseDarkColors;
      
      if (this.isDarkMode !== shouldUseDark) {
        this.logger.debug(`System theme changed, dark mode: ${shouldUseDark}`);
        
        this.isDarkMode = shouldUseDark;
        
        this.emit('theme-changed', {
          theme: this.currentTheme,
          isDark: this.isDarkMode,
          source: 'system'
        } as ThemeChangedEventArgs);
      }
    }
  }

  private shouldUseDarkMode(theme: Theme): boolean {
    switch (theme) {
      case Theme.Dark:
        return true;
      case Theme.Light:
        return false;
      case Theme.System:
        return nativeTheme.shouldUseDarkColors;
      default:
        return false;
    }
  }

  private getNativeThemeSource(theme: Theme): 'system' | 'light' | 'dark' {
    switch (theme) {
      case Theme.Dark:
        return 'dark';
      case Theme.Light:
        return 'light';
      case Theme.System:
        return 'system';
      default:
        return 'system';
    }
  }
}
