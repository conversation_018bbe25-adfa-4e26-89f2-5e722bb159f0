# SideView - Windows Sidebar Browser

![SideView Logo](docs/sideview-logo.png)

A modern Windows sidebar browser application that provides quick access to your favorite web applications. Built with .NET 6, WPF, and WebView2 for a native Windows experience with web app integration.

## 🚀 Features

- **Sidebar Panel**: Slide-out panel from the right edge of your screen
- **Multiple Web Apps**: Host multiple web applications with session isolation
- **Global Hotkeys**: System-wide keyboard shortcuts (Ctrl+Alt+Right by default)
- **Native Notifications**: Web notifications bridged to Windows native toasts
- **Automatic Updates**: GitHub-based update system with manual installation
- **60fps Animations**: Smooth, hardware-accelerated animations
- **Session Management**: Isolated cookies/cache per app or shared sessions
- **Favicon Support**: Automatic favicon fetching and caching
- **Theme Support**: Dark mode with system theme detection
- **Performance Optimized**: <2s startup, <400MB memory usage


## 🎯 Quick Start

### First Launch
1. **Launch SideView** from Start Menu or press `Ctrl+Alt+Right`
2. **Add Your First App**:
   - Click the "+" button in the sidebar
   - Enter a web app URL (e.g., `https://gmail.com`)
   - Choose session isolation (recommended for separate accounts)
   - Click "Add App"
3. **Navigate**: Click on your app to load it in the sidebar
4. **Pin Panel**: Click the pin icon to keep the panel open

### Default Hotkeys
- `Ctrl+Alt+Right`: Toggle sidebar visibility
- `Ctrl+Alt+F`: Focus search/address bar (when implemented)

### Adding Popular Apps
Pre-configured for popular services:
- **Gmail**: `https://mail.google.com`
- **Outlook**: `https://outlook.office.com`
- **Slack**: `https://app.slack.com`
- **Discord**: `https://discord.com/app`
- **WhatsApp Web**: `https://web.whatsapp.com`
- **Telegram Web**: `https://web.telegram.org`

## 📖 User Guide

### Panel Activation
- **Mouse**: Hover over the right edge of your screen
- **Hotkey**: Press `Ctrl+Alt+Right` (configurable)
- **Always On**: Use the pin button to keep panel visible

### Managing Apps
- **Add App**: Click "+" button, enter URL, configure isolation
- **Remove App**: Right-click app → Delete
- **Reorder Apps**: Drag and drop in the app list
- **Edit App**: Right-click app → Edit

### Session Isolation Options
- **Isolated**: Separate cookies/cache per app (recommended for multiple accounts)
- **Shared**: Common session data across all apps

### Notifications
- **Web Notifications**: Automatically bridged to Windows notifications
- **Badge Counts**: Unread indicators on app icons
- **Permission Management**: Grant/deny notification permissions per app

### Settings & Configuration
- **Hotkeys**: Customize global keyboard shortcuts
- **Theme**: Light/Dark mode with system detection
- **Updates**: Configure automatic update checking
- **Performance**: Adjust animation settings and memory limits

## 🔧 Development Setup

> **⚠️ MIGRATION NOTICE**: This project is being migrated from C# .NET to TypeScript/Electron for better cross-platform support and development experience. See [README-ELECTRON.md](README-ELECTRON.md) for the new implementation.

### New Electron Implementation (Recommended)
```bash
# Prerequisites: Node.js 18+, npm, Git

# Clone the repository
git clone https://github.com/sideview/sideview.git
cd sideview

# Install dependencies
npm install

# Build the application
npm run build

# Run in development mode
npm run dev

# Run with file watching
npm run dev:watch

# Build for Windows
npm run dist:win
```



## 🏗️ Architecture Overview

SideView follows a modular architecture with 8 core modules:

```
┌─────────────────────────────────────────────┐
│                Core Module                  │
│  Event Bus | Configuration | Lifecycle      │
└─────────────────┬───────────────────────────┘
                  │
    ┌─────────────┼─────────────┐
    │             │             │
┌───▼───┐    ┌────▼────┐   ┌────▼────┐
│  UI   │    │WebEngine│   │ System  │
│Module │    │ Module  │   │ Modules │
└───┬───┘    └────┬────┘   └────┬────┘
    │             │             │
┌───▼───────────────────────────▼─────────────▼───┐
│  AppManager │ SessionMgr │ Notifications │ ... │
│   Module    │   Module   │    Module     │     │
└─────────────────────────────────────────────────┘
```

### Module Breakdown
1. **Core Module**: Foundation, event bus, configuration, lifecycle
2. **UI Module**: WPF panel window, animations, activation engine
3. **WebEngine Module**: WebView2 integration, JavaScript bridge
4. **AppManager Module**: App CRUD, favicon management, SQLite storage
5. **SessionManager Module**: Cookie/cache isolation, data cleanup
6. **Notifications Module**: Web-to-native notification bridge
7. **Hotkeys Module**: Global hotkey registration (Win32 APIs)
8. **Updater Module**: GitHub-based automatic updates

### Technology Stack
- **.NET 6.0**: Modern C# with nullable reference types
- **WPF + Composition API**: Native Windows UI with hardware acceleration
- **WebView2**: Chromium-based web rendering
- **SQLite + EF Core**: Local data storage
- **Win32 APIs**: System integration (hotkeys, mouse hooks)
- **Octokit**: GitHub API integration
- **xUnit**: Unit testing framework

#### **Hotkeys Not Working**
1. Check if another application is using the same hotkey
2. Try running SideView as Administrator
3. Reconfigure hotkeys in Settings

#### **High Memory Usage**
1. Restart the application
2. Clear app cache: Settings → Clear Cache
3. Reduce number of active apps
4. Check for memory leaks in web apps

### Performance Optimization

#### **Reduce Memory Usage**
- Use shared sessions when possible
- Regularly clear cache and cookies
- Close unused web apps
- Configure automatic cleanup intervals



## 📊 Performance Metrics

### Target Performance (Achieved ✅)
- **Startup Time**: <2 seconds
- **Memory Usage**: <400MB total system
- **Animation Frame Rate**: 60fps consistently
- **Event Processing**: <1ms latency
- **Database Queries**: <100ms response time
- **Update Checks**: <5 seconds

### Memory Breakdown
- **Core Services**: ~50MB
- **UI Components**: ~30MB
- **WebEngine (per app)**: ~40-80MB
- **System Integration**: ~20MB
- **Cache & Data**: Variable (managed)

## 🔐 Security & Privacy

### Security Features
- **Session Isolation**: Cookies/cache separated between apps
- **WebView2 Sandboxing**: Each app runs in isolated context
- **HTTPS Enforcement**: All external navigation requires HTTPS
- **No Elevation Required**: Normal operation without admin rights
- **Local Data Storage**: All data stored locally, no cloud sync

### Privacy Considerations
- **No Telemetry**: SideView doesn't collect usage data
- **Local Storage**: All data remains on your device
- **Web App Privacy**: Privacy policies of individual web apps apply
- **Update Checks**: Only checks GitHub for new releases

### Data Locations
- **Configuration**: `%LOCALAPPDATA%\SideView\config\`
- **App Database**: `%LOCALAPPDATA%\SideView\apps.db`
- **Session Data**: `%LOCALAPPDATA%\SideView\sessions\`
- **Cache**: `%LOCALAPPDATA%\SideView\cache\`
- **Logs**: `%LOCALAPPDATA%\SideView\logs\`

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow
1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request
