/**
 * Main Electron process
 * Migrated from C# SideView App.xaml.cs and AppHost.cs
 */

import { app, ipcMain, screen } from 'electron';
import { AppHost } from './app-host';

class SideViewApp {
  private appHost?: AppHost;

  constructor() {
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // App event handlers
    app.whenReady().then(() => this.onReady());
    app.on('window-all-closed', () => this.onWindowAllClosed());
    app.on('activate', () => this.onActivate());
    app.on('before-quit', () => this.onBeforeQuit());

    // Security: Prevent new window creation
    app.on('web-contents-created', (_, contents) => {
      contents.setWindowOpenHandler(() => {
        return { action: 'deny' };
      });
    });

    // IPC handlers
    this.setupIpcHandlers();
  }

  private async onReady(): Promise<void> {
    try {
      console.log('Starting SideView Electron application...');

      // Create and start the application host
      this.appHost = new AppHost();
      await this.appHost.startAsync();

      console.log('SideView application started successfully!');
      console.log('Move mouse to screen edge or use hotkey to activate panel');

    } catch (error) {
      console.error('Failed to start SideView application:', error);
      app.quit();
    }
  }

  private onWindowAllClosed(): void {
    // On macOS, keep app running even when all windows are closed
    if (process.platform !== 'darwin') {
      app.quit();
    }
  }

  private onActivate(): void {
    // On macOS, re-create window when dock icon is clicked
    if (process.platform === 'darwin' && this.appHost) {
      this.appHost.showPanel();
    }
  }

  private onBeforeQuit(): void {
    // Application is quitting
  }

  private setupIpcHandlers(): void {
    // Panel control
    ipcMain.handle('panel:show', async () => {
      return this.appHost?.showPanel();
    });

    ipcMain.handle('panel:hide', async () => {
      return this.appHost?.hidePanel();
    });

    ipcMain.handle('panel:toggle', async () => {
      return this.appHost?.togglePanel();
    });

    // App management
    ipcMain.handle('apps:getAll', async () => {
      return this.appHost?.getApps();
    });

    ipcMain.handle('apps:create', async (_, request) => {
      return this.appHost?.createApp(request);
    });

    ipcMain.handle('apps:update', async (_, request) => {
      return this.appHost?.updateApp(request);
    });

    ipcMain.handle('apps:delete', async (_, appId) => {
      return this.appHost?.deleteApp(appId);
    });

    ipcMain.handle('apps:activate', async (_, appId) => {
      return this.appHost?.activateApp(appId);
    });

    // Configuration
    ipcMain.handle('config:get', async () => {
      return this.appHost?.getConfiguration();
    });

    ipcMain.handle('config:update', async (_, updates) => {
      return this.appHost?.updateConfiguration(updates);
    });

    // System info
    ipcMain.handle('system:getDisplays', async () => {
      return screen.getAllDisplays();
    });

    ipcMain.handle('system:getPrimaryDisplay', async () => {
      return screen.getPrimaryDisplay();
    });

    // Theme management
    ipcMain.handle('theme:get', async () => {
      return this.appHost?.getTheme();
    });

    ipcMain.handle('theme:set', async (_, theme) => {
      return this.appHost?.setTheme(theme);
    });

    ipcMain.handle('theme:getVariables', async () => {
      return this.appHost?.getThemeVariables();
    });

    // Additional UI handlers
    ipcMain.handle('ui:setPinned', async (_, pinned) => {
      return this.appHost?.setPinned(pinned);
    });

    ipcMain.handle('ui:isVisible', async () => {
      return this.appHost?.isVisible();
    });

    ipcMain.handle('ui:isPinned', async () => {
      return this.appHost?.isPinned();
    });

    // Tab management
    ipcMain.handle('tabs:getAll', async () => {
      return this.appHost?.getTabs();
    });

    ipcMain.handle('tabs:getActive', async () => {
      return this.appHost?.getActiveTab();
    });

    ipcMain.handle('tabs:create', async (_, request) => {
      return this.appHost?.createTab(request);
    });

    ipcMain.handle('tabs:close', async (_, tabId) => {
      return this.appHost?.closeTab(tabId);
    });

    ipcMain.handle('tabs:activate', async (_, tabId) => {
      return this.appHost?.activateTab(tabId);
    });

    ipcMain.handle('tabs:navigate', async (_, tabId, url) => {
      return this.appHost?.navigateTab(tabId, url);
    });

    ipcMain.handle('tabs:reload', async (_, tabId) => {
      return this.appHost?.reloadTab(tabId);
    });

    ipcMain.handle('tabs:getState', async () => {
      return this.appHost?.getTabManagerState();
    });
  }
}

// Prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  console.log('Another instance of SideView is already running');
  app.quit();
} else {
  app.on('second-instance', () => {
    // Someone tried to run a second instance, focus our window instead
    console.log('Second instance detected, focusing existing window');
    // Could show panel or bring to front here
  });

  // Create the application
  new SideViewApp();
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
