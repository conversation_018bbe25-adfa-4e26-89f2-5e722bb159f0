/**
 * Tests for WebEngineService
 */

import { WebEngineService } from '../webengine-service';
import { AppEventBus } from '@modules/core/event-bus';
import { ConfigurationService } from '@modules/core/configuration-service';
import { SessionManagerService } from '@modules/session/session-service';
import { WebAppModel, SessionMode } from '@shared/types/app.types';

// Mock Electron modules
jest.mock('electron', () => ({
  BrowserView: jest.fn().mockImplementation(() => ({
    webContents: {
      on: jest.fn(),
      loadURL: jest.fn(),
      reload: jest.fn(),
      executeJavaScript: jest.fn(),
      destroy: jest.fn(),
      isDestroyed: jest.fn().mockReturnValue(false),
      setUserAgent: jest.fn(),
      setZoomLevel: jest.fn()
    }
  })),
  ipcMain: {
    handle: jest.fn()
  }
}));

describe('WebEngineService', () => {
  let webEngineService: WebEngineService;
  let mockEventBus: jest.Mocked<AppEventBus>;
  let mockConfigService: jest.Mocked<ConfigurationService>;
  let mockSessionService: jest.Mocked<SessionManagerService>;
  let mockLogger: Console;

  const mockApp: WebAppModel = {
    id: 'test-app-1',
    name: 'Test App',
    url: 'https://example.com',
    sessionMode: SessionMode.Isolated,
    isActive: false,
    lastAccessed: new Date(),
    order: 0,
    isEnabled: true,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    mockEventBus = {
      publish: jest.fn(),
      subscribe: jest.fn(),
      unsubscribe: jest.fn(),
      clear: jest.fn()
    } as any;

    mockConfigService = {
      getSettings: jest.fn().mockReturnValue({
        webEngine: {
          userAgent: 'Test Agent',
          enableDevTools: false,
          defaultZoomLevel: 1.0
        }
      })
    } as any;

    mockSessionService = {
      getSessionForApp: jest.fn().mockResolvedValue({
        partition: 'test-partition'
      })
    } as any;

    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      log: jest.fn()
    } as any;

    webEngineService = new WebEngineService(
      mockEventBus,
      mockConfigService,
      mockSessionService,
      mockLogger
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      await webEngineService.initialize();
      
      expect(mockLogger.info).toHaveBeenCalledWith('Initializing WebEngine Module');
      expect(mockLogger.info).toHaveBeenCalledWith('WebEngine Module initialized successfully');
    });

    it('should not initialize twice', async () => {
      await webEngineService.initialize();
      await webEngineService.initialize();
      
      // Should only log initialization once
      expect(mockLogger.info).toHaveBeenCalledTimes(2);
    });
  });

  describe('start and stop', () => {
    beforeEach(async () => {
      await webEngineService.initialize();
    });

    it('should start successfully', async () => {
      await webEngineService.start();
      
      expect(mockEventBus.publish).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: 'WebEngineModuleStarted'
        })
      );
    });

    it('should stop successfully', async () => {
      await webEngineService.stop();
      
      expect(mockLogger.info).toHaveBeenCalledWith('Stopping WebEngine Module');
    });
  });

  describe('WebAppHost management', () => {
    beforeEach(async () => {
      await webEngineService.initialize();
    });

    it('should create WebAppHost successfully', async () => {
      const webAppHost = await webEngineService.createWebAppHost(mockApp);
      
      expect(webAppHost).toBeDefined();
      expect(mockSessionService.getSessionForApp).toHaveBeenCalledWith(mockApp);
      expect(mockLogger.info).toHaveBeenCalledWith(
        `Creating WebAppHost for app ${mockApp.id}: ${mockApp.name}`
      );
    });

    it('should switch to WebApp successfully', async () => {
      await webEngineService.createWebAppHost(mockApp);
      await webEngineService.switchToWebApp(mockApp.id);
      
      expect(mockEventBus.publish).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: 'WebAppActivated'
        })
      );
      
      const activeApp = webEngineService.getActiveWebApp();
      expect(activeApp?.id).toBe(mockApp.id);
    });

    it('should throw error when switching to non-existent app', async () => {
      await expect(webEngineService.switchToWebApp('non-existent'))
        .rejects.toThrow('WebAppHost for app non-existent not found');
    });

    it('should remove WebAppHost successfully', async () => {
      await webEngineService.createWebAppHost(mockApp);
      await webEngineService.removeWebAppHost(mockApp.id);
      
      expect(mockLogger.info).toHaveBeenCalledWith(
        `Removing WebAppHost for app ${mockApp.id}`
      );
    });

    it('should handle removing non-existent WebAppHost gracefully', async () => {
      await webEngineService.removeWebAppHost('non-existent');
      
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'WebAppHost for app non-existent not found'
      );
    });
  });

  describe('navigation', () => {
    beforeEach(async () => {
      await webEngineService.initialize();
      await webEngineService.createWebAppHost(mockApp);
    });

    it('should navigate WebApp successfully', async () => {
      const newUrl = 'https://newsite.com';
      
      await webEngineService.navigateWebApp(mockApp.id, newUrl);
      
      // Verify navigation was called on the WebAppHost
      // This would require mocking the WebAppHost implementation
    });

    it('should reload WebApp successfully', async () => {
      await webEngineService.reloadWebApp(mockApp.id);
      
      // Verify reload was called on the WebAppHost
    });

    it('should execute script in WebApp successfully', async () => {
      const script = 'console.log("test");';
      
      await webEngineService.executeScript(mockApp.id, script);
      
      // Verify script execution was called on the WebAppHost
    });
  });

  describe('error handling', () => {
    beforeEach(async () => {
      await webEngineService.initialize();
    });

    it('should handle session creation errors', async () => {
      mockSessionService.getSessionForApp.mockRejectedValue(new Error('Session error'));
      
      await expect(webEngineService.createWebAppHost(mockApp))
        .rejects.toThrow('Session error');
    });

    it('should handle navigation errors gracefully', async () => {
      await expect(webEngineService.navigateWebApp('non-existent', 'https://example.com'))
        .rejects.toThrow('WebAppHost for app non-existent not found');
    });
  });
});
