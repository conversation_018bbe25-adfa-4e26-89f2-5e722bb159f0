# SideView.Updater Module Implementation

## Overview
The Updater module handles automatic updates using Squirrel.Windows, checking for updates from GitHub releases and providing a seamless update experience.

## Architecture

```mermaid
graph TB
    A[UpdaterService] --> B[SquirrelUpdater]
    A --> C[UpdateChecker]
    A --> D[ReleaseManager]
    
    B --> E[Squirrel.exe]
    C --> F[GitHub API]
    D --> G[Download Manager]
    
    subgraph "Update Flow"
        H[Check for Updates]
        I[Download Delta]
        J[Apply Update]
        K[Restart App]
    end
    
    H --> I
    I --> J
    J --> K
```

## Key Components

### 1. Updater Service (`UpdaterService.cs`)

```mermaid
sequenceDiagram
    participant T as Timer
    participant U as UpdaterService
    participant G as GitHub API
    participant S as Squirrel
    participant A as Application
    
    T->>U: Check Updates
    U->>G: Get Latest Release
    G->>U: Release Info
    U->>U: Compare Versions
    
    alt Update Available
        U->>S: DownloadReleases()
        S->>U: Download Progress
        U->>S: ApplyReleases()
        S->>U: Update Ready
        U->>A: Prompt Restart
    end
```

```csharp
public class UpdaterService : IUpdaterService, IDisposable
{
    private readonly ILogger<UpdaterService> _logger;
    private readonly Timer _updateTimer;
    private readonly HttpClient _httpClient;
    private IUpdateManager _updateManager;
    
    public event EventHandler<UpdateEventArgs> UpdateAvailable;
    public event EventHandler<UpdateEventArgs> UpdateDownloaded;
    
    public UpdaterService(ILogger<UpdaterService> logger)
    {
        _logger = logger;
        _httpClient = new HttpClient();
        _updateTimer = new Timer(CheckForUpdates, null, 
            TimeSpan.FromMinutes(30), TimeSpan.FromHours(6));
    }
    
    public async Task InitializeAsync()
    {
        try
        {
            _updateManager = new UpdateManager(
                urlOrPath: "https://github.com/owner/sideview",
                applicationName: "SideView");
                
            _logger.LogInformation("Updater initialized");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize updater");
        }
    }
    
    private async void CheckForUpdates(object state)
    {
        try
        {
            var updateInfo = await _updateManager.CheckForUpdate();
            if (updateInfo?.ReleasesToApply?.Any() == true)
            {
                _logger.LogInformation("Update available: {Version}", 
                    updateInfo.FutureReleaseEntry.Version);
                    
                UpdateAvailable?.Invoke(this, new UpdateEventArgs(updateInfo));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking for updates");
        }
    }
    
    public async Task<bool> DownloadAndApplyUpdateAsync()
    {
        try
        {
            var updateInfo = await _updateManager.CheckForUpdate();
            if (updateInfo?.ReleasesToApply?.Any() != true)
            {
                return false;
            }
            
            await _updateManager.DownloadReleases(updateInfo.ReleasesToApply);
            await _updateManager.ApplyReleases(updateInfo);
            
            UpdateDownloaded?.Invoke(this, new UpdateEventArgs(updateInfo));
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading/applying update");
            return false;
        }
    }
}
```

## Dependencies

- `Squirrel` - Update framework
- `Octokit` - GitHub API client 