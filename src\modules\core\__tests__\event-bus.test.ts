/**
 * Tests for AppEventBus
 */

import { AppEventBus } from '../event-bus';
import { AppEvent } from '@shared/types/events.types';

class TestEvent extends AppEvent {
  constructor(public readonly data: string) {
    super('TestEvent', 'Test');
  }
}

describe('AppEventBus', () => {
  let eventBus: AppEventBus;
  let mockLogger: Console;

  beforeEach(() => {
    mockLogger = {
      debug: jest.fn(),
      error: jest.fn(),
      info: jest.fn(),
      log: jest.fn(),
      warn: jest.fn()
    } as any;
    
    eventBus = new AppEventBus(mockLogger);
  });

  afterEach(() => {
    eventBus.clear();
  });

  describe('subscribe and publish', () => {
    it('should call handler when event is published', async () => {
      const handler = jest.fn();
      const event = new TestEvent('test data');

      eventBus.subscribe('TestEvent', handler);
      await eventBus.publish(event);

      expect(handler).toHaveBeenCalledWith(event);
      expect(handler).toHaveBeenCalledTimes(1);
    });

    it('should call multiple handlers for same event type', async () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      const event = new TestEvent('test data');

      eventBus.subscribe('TestEvent', handler1);
      eventBus.subscribe('TestEvent', handler2);
      await eventBus.publish(event);

      expect(handler1).toHaveBeenCalledWith(event);
      expect(handler2).toHaveBeenCalledWith(event);
    });

    it('should handle async handlers', async () => {
      const handler = jest.fn().mockResolvedValue(undefined);
      const event = new TestEvent('test data');

      eventBus.subscribe('TestEvent', handler);
      await eventBus.publish(event);

      expect(handler).toHaveBeenCalledWith(event);
    });

    it('should not call handlers for different event types', async () => {
      const handler = jest.fn();
      const event = new TestEvent('test data');

      eventBus.subscribe('DifferentEvent', handler);
      await eventBus.publish(event);

      expect(handler).not.toHaveBeenCalled();
    });
  });

  describe('unsubscribe', () => {
    it('should remove handler when unsubscribed', async () => {
      const handler = jest.fn();
      const event = new TestEvent('test data');

      eventBus.subscribe('TestEvent', handler);
      eventBus.unsubscribe('TestEvent', handler);
      await eventBus.publish(event);

      expect(handler).not.toHaveBeenCalled();
    });

    it('should only remove specific handler', async () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      const event = new TestEvent('test data');

      eventBus.subscribe('TestEvent', handler1);
      eventBus.subscribe('TestEvent', handler2);
      eventBus.unsubscribe('TestEvent', handler1);
      await eventBus.publish(event);

      expect(handler1).not.toHaveBeenCalled();
      expect(handler2).toHaveBeenCalledWith(event);
    });
  });

  describe('clear', () => {
    it('should remove all handlers', async () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      const event = new TestEvent('test data');

      eventBus.subscribe('TestEvent', handler1);
      eventBus.subscribe('AnotherEvent', handler2);
      eventBus.clear();
      await eventBus.publish(event);

      expect(handler1).not.toHaveBeenCalled();
      expect(handler2).not.toHaveBeenCalled();
    });
  });

  describe('getHandlerCount', () => {
    it('should return correct handler count', () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();

      expect(eventBus.getHandlerCount('TestEvent')).toBe(0);

      eventBus.subscribe('TestEvent', handler1);
      expect(eventBus.getHandlerCount('TestEvent')).toBe(1);

      eventBus.subscribe('TestEvent', handler2);
      expect(eventBus.getHandlerCount('TestEvent')).toBe(2);

      eventBus.unsubscribe('TestEvent', handler1);
      expect(eventBus.getHandlerCount('TestEvent')).toBe(1);
    });
  });

  describe('getStats', () => {
    it('should return correct statistics', () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();

      eventBus.subscribe('TestEvent', handler1);
      eventBus.subscribe('TestEvent', handler2);
      eventBus.subscribe('AnotherEvent', handler1);

      const stats = eventBus.getStats();

      expect(stats.eventTypes).toBe(2);
      expect(stats.totalHandlers).toBe(3);
      expect(stats.eventTypeStats).toEqual({
        'TestEvent': 2,
        'AnotherEvent': 1
      });
    });
  });

  describe('error handling', () => {
    it('should continue processing other handlers if one throws', async () => {
      const errorHandler = jest.fn().mockImplementation(() => {
        throw new Error('Handler error');
      });
      const successHandler = jest.fn();
      const event = new TestEvent('test data');

      eventBus.subscribe('TestEvent', errorHandler);
      eventBus.subscribe('TestEvent', successHandler);
      
      await eventBus.publish(event);

      expect(errorHandler).toHaveBeenCalled();
      expect(successHandler).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should handle async handler errors', async () => {
      const errorHandler = jest.fn().mockRejectedValue(new Error('Async error'));
      const successHandler = jest.fn();
      const event = new TestEvent('test data');

      eventBus.subscribe('TestEvent', errorHandler);
      eventBus.subscribe('TestEvent', successHandler);
      
      await eventBus.publish(event);

      expect(errorHandler).toHaveBeenCalled();
      expect(successHandler).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
});
