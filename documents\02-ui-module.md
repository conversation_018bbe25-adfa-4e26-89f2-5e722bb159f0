# SideView.UI Module Implementation

## Overview
The UI module manages the main panel window, animations, themes, and user interactions. It provides a native Windows experience with fluent design principles and smooth 60fps animations.

## Architecture

```mermaid
graph TB
    A[UIService] --> B[PanelWindow]
    B --> C[ActivationEngine]
    B --> D[AnimationController]
    B --> E[ThemeManager]
    
    C --> F[MouseHookService]
    C --> G[KeyboardHookService]
    
    D --> H[CompositionAnimator]
    
    E --> I[ResourceDictionary]
    E --> J[SystemThemeWatcher]
    
    subgraph "Window Management"
        K[WindowPositioner]
        L[DpiScaleHelper]
        M[MonitorDetector]
    end
    
    B --> K
    B --> L
    B --> M
```

## Key Components

### 1. Panel Window (`PanelWindow.xaml` & `PanelWindow.xaml.cs`)

```mermaid
stateDiagram-v2
    [*] --> Hidden
    Hidden --> Activating: Mouse Hover / Hotkey
    Activating --> Visible: Animation Complete
    Visible --> Deactivating: Focus Lost / Hotkey
    Deactivating --> Hidden: Animation Complete
    
    Visible --> Pinned: Pin <PERSON><PERSON>
    Pinned --> Visible: Un<PERSON> <PERSON><PERSON>
    
    note right of Pinned
        Window stays visible
        until explicitly unpinned
    end note
```

```xaml
<Window x:Class="SideView.UI.PanelWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        Topmost="True"
        ShowInTaskbar="False"
        SizeToContent="Height"
        ResizeMode="NoResize">
    
    <Border Name="MainBorder" 
            Background="{DynamicResource PanelBackground}"
            CornerRadius="0,8,8,0"
            BorderThickness="0,1,1,1"
            BorderBrush="{DynamicResource PanelBorder}">
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Title Bar -->
            <Border Grid.Row="0" Background="{DynamicResource TitleBarBackground}">
                <Grid Height="32">
                    <TextBlock Text="SideView" 
                               VerticalAlignment="Center" 
                               Margin="12,0"/>
                    <StackPanel Orientation="Horizontal" 
                                HorizontalAlignment="Right">
                        <Button Name="PinButton" Style="{StaticResource TitleBarButton}"/>
                        <Button Name="SettingsButton" Style="{StaticResource TitleBarButton}"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- App Bar -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <ItemsControl Name="AppList" ItemsSource="{Binding Apps}"/>
            </ScrollViewer>
            
            <!-- Browser Content -->
            <ContentPresenter Grid.Row="2" 
                              Name="BrowserHost" 
                              Content="{Binding ActiveWebView}"/>
        </Grid>
    </Border>
</Window>
```

### 2. Activation Engine (`ActivationEngine.cs`)

```mermaid
sequenceDiagram
    participant U as User
    participant M as Mouse Monitor
    participant A as ActivationEngine
    participant W as PanelWindow
    participant T as Timer
    
    U->>M: Mouse Move
    M->>A: OnMouseMove(x, y)
    A->>A: Check Hit Test
    alt Mouse in activation zone
        A->>T: Start Delay Timer
        T->>A: Timer Elapsed
        A->>W: ShowAsync()
        W->>W: Animate In
    else Mouse outside zone
        A->>T: Cancel Timer
        A->>W: HideAsync()
        W->>W: Animate Out
    end
```

```csharp
public class ActivationEngine : IDisposable
{
    private readonly ILogger<ActivationEngine> _logger;
    private readonly PanelWindow _window;
    private readonly Timer _activationTimer;
    private readonly Timer _deactivationTimer;
    private readonly MouseHookService _mouseHook;
    private readonly HotkeyService _hotkeyService;
    
    private bool _isActivating;
    private bool _isPinned;
    private Point _lastMousePosition;
    
    public ActivationEngine(
        PanelWindow window,
        MouseHookService mouseHook,
        HotkeyService hotkeyService,
        ILogger<ActivationEngine> logger)
    {
        _window = window;
        _mouseHook = mouseHook;
        _hotkeyService = hotkeyService;
        _logger = logger;
        
        _activationTimer = new Timer(OnActivationTimer, null, Timeout.Infinite, Timeout.Infinite);
        _deactivationTimer = new Timer(OnDeactivationTimer, null, Timeout.Infinite, Timeout.Infinite);
        
        SetupEventHandlers();
    }
    
    private void SetupEventHandlers()
    {
        _mouseHook.MouseMove += OnGlobalMouseMove;
        _hotkeyService.HotkeyPressed += OnHotkeyPressed;
        _window.MouseEnter += OnWindowMouseEnter;
        _window.MouseLeave += OnWindowMouseLeave;
        _window.LostFocus += OnWindowLostFocus;
    }
    
    private void OnGlobalMouseMove(object sender, MouseEventArgs e)
    {
        _lastMousePosition = e.Position;
        
        if (_isPinned) return;
        
        var activationZone = GetActivationZone();
        var isInZone = activationZone.Contains(e.Position);
        
        if (isInZone && !_window.IsVisible && !_isActivating)
        {
            _isActivating = true;
            _activationTimer.Change(Settings.ActivationDelay, Timeout.Infinite);
        }
        else if (!isInZone && !_window.IsMouseOver)
        {
            _activationTimer.Change(Timeout.Infinite, Timeout.Infinite);
            _isActivating = false;
            
            if (_window.IsVisible)
            {
                _deactivationTimer.Change(Settings.DeactivationDelay, Timeout.Infinite);
            }
        }
    }
    
    private Rectangle GetActivationZone()
    {
        var screen = Screen.FromPoint(_lastMousePosition);
        var bounds = screen.WorkingArea;
        
        return Settings.PanelPosition switch
        {
            PanelPosition.Left => new Rectangle(
                bounds.Left, bounds.Top, 
                Settings.ActivationStripWidth, bounds.Height),
            PanelPosition.Right => new Rectangle(
                bounds.Right - Settings.ActivationStripWidth, bounds.Top,
                Settings.ActivationStripWidth, bounds.Height),
            _ => throw new ArgumentOutOfRangeException()
        };
    }
}
```

### 3. Animation Controller (`AnimationController.cs`)

```mermaid
flowchart TD
    A[Animation Request] --> B{Animation Type}
    B -->|Show| C[Slide In Animation]
    B -->|Hide| D[Slide Out Animation]
    B -->|Resize| E[Scale Animation]
    
    C --> F[Create Composition Animation]
    D --> F
    E --> F
    
    F --> G[Set Easing Function]
    G --> H[Configure Duration]
    H --> I[Start Animation]
    I --> J[Monitor Progress]
    J --> K[Animation Complete]
    
    subgraph "Easing Functions"
        L[EaseOutQuart]
        M[EaseInOutCubic]
        N[Spring]
    end
    
    G --> L
    G --> M
    G --> N
```

```csharp
public class AnimationController
{
    private readonly Compositor _compositor;
    private readonly Visual _windowVisual;
    private readonly ILogger<AnimationController> _logger;
    
    public AnimationController(Window window, ILogger<AnimationController> logger)
    {
        _logger = logger;
        _compositor = WindowsComposition.GetCompositor(window);
        _windowVisual = WindowsComposition.GetVisual(window);
    }
    
    public async Task ShowAsync(PanelPosition position, TimeSpan duration)
    {
        var animation = CreateSlideInAnimation(position, duration);
        _windowVisual.StartAnimation("Translation", animation);
        
        // Also animate opacity
        var opacityAnimation = _compositor.CreateScalarKeyFrameAnimation();
        opacityAnimation.Duration = duration;
        opacityAnimation.InsertKeyFrame(0f, 0f);
        opacityAnimation.InsertKeyFrame(1f, 1f);
        
        _windowVisual.StartAnimation("Opacity", opacityAnimation);
        
        await WaitForAnimationAsync(animation);
    }
    
    public async Task HideAsync(PanelPosition position, TimeSpan duration)
    {
        var animation = CreateSlideOutAnimation(position, duration);
        _windowVisual.StartAnimation("Translation", animation);
        
        var opacityAnimation = _compositor.CreateScalarKeyFrameAnimation();
        opacityAnimation.Duration = duration;
        opacityAnimation.InsertKeyFrame(0f, 1f);
        opacityAnimation.InsertKeyFrame(1f, 0f);
        
        _windowVisual.StartAnimation("Opacity", opacityAnimation);
        
        await WaitForAnimationAsync(animation);
    }
    
    private ScalarKeyFrameAnimation CreateSlideInAnimation(PanelPosition position, TimeSpan duration)
    {
        var animation = _compositor.CreateScalarKeyFrameAnimation();
        animation.Duration = duration;
        
        var startValue = position == PanelPosition.Left ? -_window.ActualWidth : _window.ActualWidth;
        
        animation.InsertKeyFrame(0f, startValue);
        animation.InsertKeyFrame(1f, 0f);
        animation.IterationBehavior = AnimationIterationBehavior.Count;
        animation.IterationCount = 1;
        
        // Use custom easing for smooth animation
        var easingFunction = _compositor.CreateCubicBezierEasingFunction(
            new Vector2(0.25f, 0.1f), new Vector2(0.25f, 1f));
        animation.InsertKeyFrame(1f, 0f, easingFunction);
        
        return animation;
    }
}
```

### 4. Theme Management (`ThemeManager.cs`)

```mermaid
graph LR
    A[System Theme Change] --> B[ThemeManager]
    B --> C{Current Theme}
    C -->|Light| D[Load Light Resources]
    C -->|Dark| E[Load Dark Resources]
    C -->|Auto| F[Detect System Theme]
    
    F --> G{System Dark Mode?}
    G -->|Yes| E
    G -->|No| D
    
    D --> H[Apply Resources]
    E --> H
    H --> I[Notify Components]
    I --> J[Update Window Styling]
    
    subgraph "Resources"
        K[Colors.xaml]
        L[Brushes.xaml]
        M[Styles.xaml]
    end
    
    H --> K
    H --> L
    H --> M
```

```csharp
public class ThemeManager : INotifyPropertyChanged
{
    private readonly ResourceDictionary _lightTheme;
    private readonly ResourceDictionary _darkTheme;
    private readonly SystemThemeWatcher _systemWatcher;
    private ThemeMode _currentTheme = ThemeMode.Auto;
    
    public event PropertyChangedEventHandler PropertyChanged;
    public event EventHandler<ThemeChangedEventArgs> ThemeChanged;
    
    public ThemeMode CurrentTheme
    {
        get => _currentTheme;
        set
        {
            if (_currentTheme != value)
            {
                var oldTheme = _currentTheme;
                _currentTheme = value;
                ApplyTheme();
                OnPropertyChanged();
                ThemeChanged?.Invoke(this, new ThemeChangedEventArgs(oldTheme, value));
            }
        }
    }
    
    public ThemeManager()
    {
        LoadThemeResources();
        _systemWatcher = new SystemThemeWatcher();
        _systemWatcher.ThemeChanged += OnSystemThemeChanged;
        
        // Apply initial theme
        ApplyTheme();
    }
    
    private void LoadThemeResources()
    {
        _lightTheme = new ResourceDictionary
        {
            Source = new Uri("/SideView.UI;component/Themes/Light.xaml", UriKind.Relative)
        };
        
        _darkTheme = new ResourceDictionary
        {
            Source = new Uri("/SideView.UI;component/Themes/Dark.xaml", UriKind.Relative)
        };
    }
    
    private void ApplyTheme()
    {
        var targetTheme = ResolveActualTheme();
        var resources = targetTheme == ActualTheme.Light ? _lightTheme : _darkTheme;
        
        Application.Current.Dispatcher.BeginInvoke(() =>
        {
            Application.Current.Resources.MergedDictionaries.Clear();
            Application.Current.Resources.MergedDictionaries.Add(resources);
        });
    }
    
    private ActualTheme ResolveActualTheme()
    {
        return _currentTheme switch
        {
            ThemeMode.Light => ActualTheme.Light,
            ThemeMode.Dark => ActualTheme.Dark,
            ThemeMode.Auto => _systemWatcher.IsSystemDarkMode ? ActualTheme.Dark : ActualTheme.Light,
            _ => ActualTheme.Light
        };
    }
}
```

## Window Positioning Logic

```mermaid
flowchart TD
    A[Position Window] --> B[Get Current Monitor]
    B --> C[Calculate DPI Scale]
    C --> D{Panel Position}
    D -->|Left| E[X = WorkArea.Left]
    D -->|Right| F[X = WorkArea.Right - Width]
    
    E --> G[Y = WorkArea.Top]
    F --> G
    G --> H[Height = WorkArea.Height]
    H --> I[Apply DPI Scaling]
    I --> J[Set Window Bounds]
    
    subgraph "Multi-Monitor Support"
        K[Detect Monitor Change]
        L[Recalculate Position]
        M[Animate to New Position]
    end
    
    J --> K
    K --> L
    L --> M
    M --> J
```

## Performance Optimizations

```csharp
public class PerformanceOptimizations
{
    // Use composition for hardware acceleration
    private void EnableComposition(Window window)
    {
        var hwndSource = PresentationSource.FromVisual(window) as HwndSource;
        var compositor = WindowsComposition.GetCompositor(window);
        
        // Enable hardware acceleration
        compositor.CreateHostBackdropBrush();
    }
    
    // Virtualize app list for large collections
    private void SetupVirtualization()
    {
        var appList = FindChild<ItemsControl>("AppList");
        appList.ItemsPanel = new ItemsPanelTemplate(new FrameworkElementFactory(typeof(VirtualizingStackPanel)));
        VirtualizingPanel.SetIsVirtualizing(appList, true);
        VirtualizingPanel.SetVirtualizationMode(appList, VirtualizationMode.Recycling);
    }
    
    // Optimize rendering during animations
    private void OptimizeForAnimation(Visual visual)
    {
        RenderOptions.SetBitmapScalingMode(visual, BitmapScalingMode.LowQuality);
        RenderOptions.SetCachingHint(visual, CachingHint.Cache);
    }
}
```

## Event Flow

```mermaid
sequenceDiagram
    participant U as User
    participant M as MouseHook
    participant A as ActivationEngine
    participant W as Window
    participant T as ThemeManager
    participant C as AnimationController
    
    U->>M: Mouse near edge
    M->>A: OnMouseMove
    A->>A: Check activation zone
    A->>C: ShowAsync()
    C->>W: Start slide animation
    W->>W: Window becomes visible
    
    U->>W: Click app icon
    W->>A: OnAppSelected
    A->>+W: Navigate to app
    
    Note over T: System theme changes
    T->>T: Detect theme change
    T->>W: Apply new theme
    W->>W: Update visual styling
```

## Key XAML Resources

```xaml
<!-- Themes/Dark.xaml -->
<ResourceDictionary>
    <SolidColorBrush x:Key="PanelBackground" Color="#2D2D30"/>
    <SolidColorBrush x:Key="PanelBorder" Color="#3F3F46"/>
    <SolidColorBrush x:Key="TitleBarBackground" Color="#252526"/>
    <SolidColorBrush x:Key="AccentBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="TextPrimary" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="TextSecondary" Color="#CCCCCC"/>
    
    <Style x:Key="TitleBarButton" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Width" Value="32"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#404040"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>
```

## Testing Strategies

```csharp
[Fact]
public async Task ActivationEngine_MouseInZone_ShowsPanel()
{
    // Arrange
    var engine = CreateActivationEngine();
    var mousePos = new Point(2, 100); // Within activation zone
    
    // Act
    engine.SimulateMouseMove(mousePos);
    await Task.Delay(Settings.ActivationDelay + 50);
    
    // Assert
    Assert.True(_window.IsVisible);
}

[Fact]
public void ThemeManager_SystemThemeChange_UpdatesTheme()
{
    // Arrange
    var themeManager = new ThemeManager();
    themeManager.CurrentTheme = ThemeMode.Auto;
    
    // Act
    _systemThemeWatcher.SimulateThemeChange(SystemTheme.Dark);
    
    // Assert
    Assert.Equal(ActualTheme.Dark, themeManager.ResolvedTheme);
}
```

## Dependencies

- `Microsoft.Toolkit.Win32.UI.Controls` - WinUI 3 controls
- `Microsoft.Windows.CsWin32` - Win32 API access
- `CommunityToolkit.Mvvm` - MVVM helpers
- `System.Drawing.Common` - Screen/Monitor APIs 