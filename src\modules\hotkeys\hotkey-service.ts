/**
 * Hotkey Service - Global keyboard shortcuts
 * Migrated from C# SideView.Hotkeys.Services.HotkeyService
 */

import { AppEventBus } from '@modules/core/event-bus';

export class HotkeyService {
  private readonly _eventBus: AppEventBus;
  private readonly logger: Console;

  constructor(
    eventBus: AppEventBus,
    logger: Console = console
  ) {
    this._eventBus = eventBus;
    this.logger = logger;
    void this._eventBus; // Mark as used for stub implementation
  }

  async initialize(): Promise<void> {
    this.logger.info('Initializing Hotkeys Module');
    // TODO: Initialize global hotkeys
    this.logger.info('Hotkeys Module initialized successfully');
  }

  async start(): Promise<void> {
    this.logger.info('Starting Hotkeys Module');
    this.logger.info('Hotkeys Module started successfully');
  }

  async stop(): Promise<void> {
    this.logger.info('Stopping Hotkeys Module');
    this.logger.info('Hotkeys Module stopped successfully');
  }
}
