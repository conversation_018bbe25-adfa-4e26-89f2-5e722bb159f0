/**
 * Dependency Injection Service Container
 * Migrated from C# Microsoft.Extensions.DependencyInjection
 */

export type ServiceLifetime = 'singleton' | 'transient' | 'scoped';

export interface ServiceDescriptor<T = any> {
  token: string | symbol | Function;
  implementation?: new (...args: any[]) => T;
  factory?: (container: ServiceContainer) => T;
  instance?: T;
  lifetime: ServiceLifetime;
  dependencies?: (string | symbol | Function)[];
}

export class ServiceContainer {
  private readonly services = new Map<string | symbol | Function, ServiceDescriptor>();
  private readonly instances = new Map<string | symbol | Function, any>();
  private readonly logger: Console;

  constructor(logger: Console = console) {
    this.logger = logger;
  }

  /**
   * Registers a singleton service
   */
  addSingleton<T>(
    token: string | symbol | Function,
    implementation?: new (...args: any[]) => T,
    factory?: (container: ServiceContainer) => T
  ): ServiceContainer {
    return this.addService(token, implementation, factory, 'singleton');
  }

  /**
   * Registers a transient service
   */
  addTransient<T>(
    token: string | symbol | Function,
    implementation?: new (...args: any[]) => T,
    factory?: (container: ServiceContainer) => T
  ): ServiceContainer {
    return this.addService(token, implementation, factory, 'transient');
  }

  /**
   * Registers a scoped service
   */
  addScoped<T>(
    token: string | symbol | Function,
    implementation?: new (...args: any[]) => T,
    factory?: (container: ServiceContainer) => T
  ): ServiceContainer {
    return this.addService(token, implementation, factory, 'scoped');
  }

  /**
   * Registers a service instance
   */
  addInstance<T>(token: string | symbol | Function, instance: T): ServiceContainer {
    this.services.set(token, {
      token,
      instance,
      lifetime: 'singleton'
    });

    this.instances.set(token, instance);
    this.logger.debug(`Registered service instance: ${this.getTokenName(token)}`);
    return this;
  }

  /**
   * Gets a service instance
   */
  get<T>(token: string | symbol | Function): T {
    const service = this.services.get(token);
    if (!service) {
      throw new Error(`Service not registered: ${this.getTokenName(token)}`);
    }

    // Return existing instance for singletons
    if (service.lifetime === 'singleton' && this.instances.has(token)) {
      return this.instances.get(token);
    }

    // Create new instance
    const instance = this.createInstance<T>(service);

    // Store singleton instances
    if (service.lifetime === 'singleton') {
      this.instances.set(token, instance);
    }

    return instance;
  }

  /**
   * Gets a service instance if registered, otherwise returns undefined
   */
  tryGet<T>(token: string | symbol | Function): T | undefined {
    try {
      return this.get<T>(token);
    } catch {
      return undefined;
    }
  }

  /**
   * Checks if a service is registered
   */
  has(token: string | symbol | Function): boolean {
    return this.services.has(token);
  }

  /**
   * Gets all registered service tokens
   */
  getRegisteredServices(): (string | symbol | Function)[] {
    return Array.from(this.services.keys());
  }

  /**
   * Clears all services and instances
   */
  clear(): void {
    this.services.clear();
    this.instances.clear();
    this.logger.debug('Service container cleared');
  }

  private addService<T>(
    token: string | symbol | Function,
    implementation?: new (...args: any[]) => T,
    factory?: (container: ServiceContainer) => T,
    lifetime: ServiceLifetime = 'singleton'
  ): ServiceContainer {
    if (!implementation && !factory) {
      throw new Error(`Either implementation or factory must be provided for service: ${this.getTokenName(token)}`);
    }

    const descriptor: ServiceDescriptor<T> = {
      token,
      lifetime
    };

    if (implementation) {
      descriptor.implementation = implementation;
    }

    if (factory) {
      descriptor.factory = factory;
    }

    this.services.set(token, descriptor);

    this.logger.debug(`Registered ${lifetime} service: ${this.getTokenName(token)}`);
    return this;
  }

  private createInstance<T>(service: ServiceDescriptor<T>): T {
    // Use factory if provided
    if (service.factory) {
      return service.factory(this);
    }

    // Use existing instance if provided
    if (service.instance) {
      return service.instance;
    }

    // Create instance from implementation
    if (service.implementation) {
      const dependencies = this.resolveDependencies(service.dependencies || []);
      return new service.implementation(...dependencies);
    }

    throw new Error(`Cannot create instance for service: ${this.getTokenName(service.token)}`);
  }

  private resolveDependencies(dependencies: (string | symbol | Function)[]): any[] {
    return dependencies.map(dep => this.get(dep));
  }

  private getTokenName(token: string | symbol | Function): string {
    if (typeof token === 'string') {
      return token;
    }
    if (typeof token === 'symbol') {
      return token.toString();
    }
    if (typeof token === 'function') {
      return token.name || 'Anonymous';
    }
    return 'Unknown';
  }
}
