# SideView.Notifications Module Implementation

## Overview
The Notifications module bridges web notifications from WebView2 to native Windows toast notifications, providing badge counts and notification management.

## Architecture

```mermaid
graph TB
    A[NotificationService] --> B[ToastManager]
    A --> C[BadgeManager]
    A --> D[PermissionManager]
    
    B --> E[WinRT Toast APIs]
    C --> F[Taskbar Badge]
    D --> G[WebView2 Permissions]
    
    subgraph "Notification Flow"
        H[Web Notification]
        I[WebView2 Event]
        J[Native Toast]
        K[User Interaction]
    end
    
    H --> I
    I --> A
    A --> J
    J --> K
```

## Key Components

### 1. Notification Service (`NotificationService.cs`)

```mermaid
sequenceDiagram
    participant W as WebView2
    participant N as NotificationService
    participant T as ToastManager
    participant B as BadgeManager
    participant U as User
    
    W->>N: PermissionRequested
    N->>N: Check Settings
    N->>W: Grant/Deny Permission
    
    W->>N: NotificationReceived
    N->>T: ShowToast()
    N->>B: UpdateBadge()
    T->>U: Display Toast
    
    U->>T: Click Toast
    T->>N: OnToastActivated
    N->>W: FocusApp()
```

```csharp
public class NotificationService : INotificationService
{
    private readonly ILogger<NotificationService> _logger;
    private readonly ToastManager _toastManager;
    private readonly BadgeManager _badgeManager;
    private readonly ConcurrentDictionary<Guid, NotificationSettings> _appSettings;
    
    public NotificationService(ILogger<NotificationService> logger)
    {
        _logger = logger;
        _toastManager = new ToastManager();
        _badgeManager = new BadgeManager();
        _appSettings = new ConcurrentDictionary<Guid, NotificationSettings>();
    }
    
    public async Task<bool> ShowNotificationAsync(Guid appId, WebNotification notification)
    {
        if (!_appSettings.TryGetValue(appId, out var settings) || !settings.Enabled)
        {
            return false;
        }
        
        var toast = await _toastManager.CreateToastAsync(appId, notification);
        await _toastManager.ShowAsync(toast);
        
        _badgeManager.IncrementBadge(appId);
        
        return true;
    }
}
```

## Dependencies

- `Microsoft.Toolkit.Win32.UI.Controls` - WinRT APIs
- `Windows.UI.Notifications` - Toast notifications 