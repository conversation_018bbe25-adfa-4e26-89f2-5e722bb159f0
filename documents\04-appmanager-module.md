# SideView.AppManager Module Implementation

## Overview
The AppManager module handles CRUD operations for web applications, favicon management, app ordering, and persistence. It provides a repository pattern over SQLite with caching for performance.

## Architecture

```mermaid
graph TB
    A[AppManagerService] --> B[IAppRepository]
    B --> C[AppRepository]
    C --> D[SideViewDbContext]
    D --> E[SQLite Database]
    
    A --> F[FaviconService]
    F --> G[HttpClient]
    F --> H[IconCache]
    
    A --> I[AppValidator]
    A --> J[UrlNormalizer]
    
    subgraph "Data Models"
        K[AppModel]
        L[AppSettings]
        M[IconData]
    end
    
    C --> K
    A --> L
    F --> M
```

## Key Components

### 1. App Manager Service (`AppManagerService.cs`)

```mermaid
stateDiagram-v2
    [*] --> Initializing
    Initializing --> Ready: Load Apps
    Ready --> Creating: CreateApp()
    Creating --> Validating: Validate Data
    Validating --> Saving: Save to DB
    Saving --> FetchingIcon: Fetch Favicon
    FetchingIcon --> Ready: Icon Cached
    
    Ready --> Updating: UpdateApp()
    Updating --> Validating
    
    Ready --> Deleting: DeleteApp()
    Deleting --> Cleanup: Remove Data
    Cleanup --> Ready: Complete
    
    Ready --> Reordering: ReorderApps()
    Reordering --> Ready: Order Saved
```

```csharp
public class AppManagerService : IAppManagerService
{
    private readonly IAppRepository _repository;
    private readonly FaviconService _faviconService;
    private readonly IAppEventBus _eventBus;
    private readonly ILogger<AppManagerService> _logger;
    private readonly SemaphoreSlim _semaphore;
    
    private readonly ObservableCollection<AppModel> _apps;
    private readonly ConcurrentDictionary<Guid, AppModel> _appCache;
    
    public IReadOnlyList<AppModel> Apps => _apps;
    
    public event EventHandler<AppEventArgs> AppAdded;
    public event EventHandler<AppEventArgs> AppUpdated;
    public event EventHandler<AppEventArgs> AppDeleted;
    public event EventHandler<AppsReorderedEventArgs> AppsReordered;
    
    public AppManagerService(
        IAppRepository repository,
        FaviconService faviconService,
        IAppEventBus eventBus,
        ILogger<AppManagerService> logger)
    {
        _repository = repository;
        _faviconService = faviconService;
        _eventBus = eventBus;
        _logger = logger;
        _semaphore = new SemaphoreSlim(1, 1);
        
        _apps = new ObservableCollection<AppModel>();
        _appCache = new ConcurrentDictionary<Guid, AppModel>();
    }
    
    public async Task InitializeAsync()
    {
        await _semaphore.WaitAsync();
        try
        {
            _logger.LogInformation("Initializing AppManager");
            
            var apps = await _repository.GetAllAsync();
            
            _apps.Clear();
            _appCache.Clear();
            
            foreach (var app in apps.OrderBy(a => a.Order))
            {
                _apps.Add(app);
                _appCache[app.Id] = app;
            }
            
            _logger.LogInformation("Loaded {Count} apps", _apps.Count);
        }
        finally
        {
            _semaphore.Release();
        }
    }
    
    public async Task<AppModel> CreateAppAsync(CreateAppRequest request)
    {
        await _semaphore.WaitAsync();
        try
        {
            _logger.LogInformation("Creating new app: {Name} - {Url}", request.Name, request.Url);
            
            // Validate request
            var validationResult = await ValidateCreateRequestAsync(request);
            if (!validationResult.IsValid)
            {
                throw new ValidationException(validationResult.ErrorMessage);
            }
            
            // Normalize URL
            var normalizedUrl = UrlNormalizer.Normalize(request.Url);
            
            // Create app model
            var app = new AppModel
            {
                Id = Guid.NewGuid(),
                Name = request.Name.Trim(),
                Url = normalizedUrl,
                SessionMode = request.SessionMode,
                Order = _apps.Count,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsEnabled = true
            };
            
            // Save to database
            await _repository.CreateAsync(app);
            
            // Add to collections
            _apps.Add(app);
            _appCache[app.Id] = app;
            
            // Fetch favicon asynchronously
            _ = Task.Run(async () =>
            {
                try
                {
                    var iconData = await _faviconService.FetchFaviconAsync(app.Url);
                    if (iconData != null)
                    {
                        app.IconPath = iconData.LocalPath;
                        await _repository.UpdateAsync(app);
                        
                        await _eventBus.PublishAsync(new AppIconUpdatedEvent(app.Id, iconData.LocalPath));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to fetch favicon for app {AppId}", app.Id);
                }
            });
            
            // Publish event
            AppAdded?.Invoke(this, new AppEventArgs(app));
            await _eventBus.PublishAsync(new AppCreatedEvent(app));
            
            _logger.LogInformation("App created successfully: {AppId}", app.Id);
            return app;
        }
        finally
        {
            _semaphore.Release();
        }
    }
    
    public async Task<AppModel> UpdateAppAsync(Guid appId, UpdateAppRequest request)
    {
        await _semaphore.WaitAsync();
        try
        {
            if (!_appCache.TryGetValue(appId, out var app))
            {
                throw new AppNotFoundException($"App with ID {appId} not found");
            }
            
            _logger.LogInformation("Updating app {AppId}: {Name}", appId, request.Name);
            
            // Validate request
            var validationResult = await ValidateUpdateRequestAsync(appId, request);
            if (!validationResult.IsValid)
            {
                throw new ValidationException(validationResult.ErrorMessage);
            }
            
            var oldUrl = app.Url;
            var urlChanged = false;
            
            // Update properties
            if (!string.IsNullOrEmpty(request.Name))
            {
                app.Name = request.Name.Trim();
            }
            
            if (!string.IsNullOrEmpty(request.Url))
            {
                var normalizedUrl = UrlNormalizer.Normalize(request.Url);
                if (app.Url != normalizedUrl)
                {
                    app.Url = normalizedUrl;
                    urlChanged = true;
                }
            }
            
            if (request.SessionMode.HasValue)
            {
                app.SessionMode = request.SessionMode.Value;
            }
            
            if (request.IsEnabled.HasValue)
            {
                app.IsEnabled = request.IsEnabled.Value;
            }
            
            app.UpdatedAt = DateTime.UtcNow;
            
            // Save to database
            await _repository.UpdateAsync(app);
            
            // Fetch new favicon if URL changed
            if (urlChanged)
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        var iconData = await _faviconService.FetchFaviconAsync(app.Url);
                        if (iconData != null)
                        {
                            app.IconPath = iconData.LocalPath;
                            await _repository.UpdateAsync(app);
                            
                            await _eventBus.PublishAsync(new AppIconUpdatedEvent(app.Id, iconData.LocalPath));
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to fetch favicon for updated app {AppId}", app.Id);
                    }
                });
            }
            
            // Publish events
            AppUpdated?.Invoke(this, new AppEventArgs(app));
            await _eventBus.PublishAsync(new AppUpdatedEvent(app, oldUrl));
            
            _logger.LogInformation("App updated successfully: {AppId}", app.Id);
            return app;
        }
        finally
        {
            _semaphore.Release();
        }
    }
    
    private async Task<ValidationResult> ValidateCreateRequestAsync(CreateAppRequest request)
    {
        // Name validation
        if (string.IsNullOrWhiteSpace(request.Name))
        {
            return ValidationResult.Invalid("App name is required");
        }
        
        if (request.Name.Length > 100)
        {
            return ValidationResult.Invalid("App name cannot exceed 100 characters");
        }
        
        // Check for duplicate name
        if (_apps.Any(a => a.Name.Equals(request.Name.Trim(), StringComparison.OrdinalIgnoreCase)))
        {
            return ValidationResult.Invalid("An app with this name already exists");
        }
        
        // URL validation
        if (string.IsNullOrWhiteSpace(request.Url))
        {
            return ValidationResult.Invalid("URL is required");
        }
        
        if (!Uri.TryCreate(request.Url, UriKind.Absolute, out var uri) ||
            (uri.Scheme != "http" && uri.Scheme != "https"))
        {
            return ValidationResult.Invalid("Invalid URL format");
        }
        
        // Check for duplicate URL
        var normalizedUrl = UrlNormalizer.Normalize(request.Url);
        if (_apps.Any(a => a.Url.Equals(normalizedUrl, StringComparison.OrdinalIgnoreCase)))
        {
            return ValidationResult.Invalid("An app with this URL already exists");
        }
        
        return ValidationResult.Valid();
    }
}
```

### 2. App Repository (`AppRepository.cs`)

```mermaid
sequenceDiagram
    participant S as Service
    participant R as Repository
    participant C as DbContext
    participant D as SQLite DB
    
    S->>R: GetAllAsync()
    R->>C: Apps.ToListAsync()
    C->>D: SELECT * FROM Apps
    D->>C: Result Set
    C->>R: List<AppModel>
    R->>S: Apps with tracking
    
    S->>R: CreateAsync(app)
    R->>C: Apps.Add(app)
    R->>C: SaveChangesAsync()
    C->>D: INSERT INTO Apps
    D->>C: Success
    C->>R: Changes Saved
    R->>S: App Created
```

```csharp
public class AppRepository : IAppRepository
{
    private readonly SideViewDbContext _context;
    private readonly ILogger<AppRepository> _logger;
    
    public AppRepository(SideViewDbContext context, ILogger<AppRepository> logger)
    {
        _context = context;
        _logger = logger;
    }
    
    public async Task<IList<AppModel>> GetAllAsync()
    {
        try
        {
            return await _context.Apps
                .Where(a => !a.IsDeleted)
                .OrderBy(a => a.Order)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve apps from database");
            throw;
        }
    }
    
    public async Task<AppModel?> GetByIdAsync(Guid id)
    {
        try
        {
            return await _context.Apps
                .FirstOrDefaultAsync(a => a.Id == id && !a.IsDeleted);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve app {AppId} from database", id);
            throw;
        }
    }
    
    public async Task<AppModel> CreateAsync(AppModel app)
    {
        try
        {
            _context.Apps.Add(app);
            await _context.SaveChangesAsync();
            
            _logger.LogDebug("App {AppId} created in database", app.Id);
            return app;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create app {AppId} in database", app.Id);
            throw;
        }
    }
    
    public async Task<AppModel> UpdateAsync(AppModel app)
    {
        try
        {
            _context.Apps.Update(app);
            await _context.SaveChangesAsync();
            
            _logger.LogDebug("App {AppId} updated in database", app.Id);
            return app;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update app {AppId} in database", app.Id);
            throw;
        }
    }
    
    public async Task DeleteAsync(Guid id)
    {
        try
        {
            var app = await GetByIdAsync(id);
            if (app != null)
            {
                // Soft delete
                app.IsDeleted = true;
                app.DeletedAt = DateTime.UtcNow;
                
                _context.Apps.Update(app);
                await _context.SaveChangesAsync();
                
                _logger.LogDebug("App {AppId} deleted from database", id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete app {AppId} from database", id);
            throw;
        }
    }
    
    public async Task UpdateOrderAsync(IList<(Guid Id, int Order)> orderUpdates)
    {
        try
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            foreach (var (id, order) in orderUpdates)
            {
                await _context.Database.ExecuteSqlRawAsync(
                    "UPDATE Apps SET [Order] = {0}, UpdatedAt = {1} WHERE Id = {2}",
                    order, DateTime.UtcNow, id);
            }
            
            await transaction.CommitAsync();
            _logger.LogDebug("Updated order for {Count} apps", orderUpdates.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update app order in database");
            throw;
        }
    }
}
```

### 3. Favicon Service (`FaviconService.cs`)

```mermaid
flowchart TD
    A[Fetch Favicon Request] --> B[Parse Domain]
    B --> C[Check Cache]
    C --> D{Cached?}
    D -->|Yes| E[Return Cached Icon]
    D -->|No| F[Try Standard Paths]
    
    F --> G["/favicon.ico"]
    F --> H["/favicon.png"]
    F --> I[Parse HTML for Links]
    
    G --> J{Found?}
    H --> J
    I --> J
    
    J -->|Yes| K[Download Icon]
    J -->|No| L[Try Google S2]
    
    K --> M[Validate Image]
    L --> M
    
    M --> N{Valid?}
    N -->|Yes| O[Cache Icon]
    N -->|No| P[Use Default Icon]
    
    O --> Q[Return Icon Data]
    P --> Q
```

```csharp
public class FaviconService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<FaviconService> _logger;
    private readonly string _cacheDirectory;
    private readonly ConcurrentDictionary<string, IconData> _memoryCache;
    
    public FaviconService(HttpClient httpClient, ILogger<FaviconService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _cacheDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
            "SideView", "Icons");
            
        _memoryCache = new ConcurrentDictionary<string, IconData>();
        
        Directory.CreateDirectory(_cacheDirectory);
        
        // Configure HttpClient
        _httpClient.Timeout = TimeSpan.FromSeconds(10);
        _httpClient.DefaultRequestHeaders.Add("User-Agent", 
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) SideView/1.0");
    }
    
    public async Task<IconData?> FetchFaviconAsync(string url)
    {
        try
        {
            var uri = new Uri(url);
            var domain = uri.Host;
            
            _logger.LogDebug("Fetching favicon for domain: {Domain}", domain);
            
            // Check memory cache first
            if (_memoryCache.TryGetValue(domain, out var cachedIcon))
            {
                _logger.LogDebug("Favicon cache hit for domain: {Domain}", domain);
                return cachedIcon;
            }
            
            // Check disk cache
            var diskCacheResult = await CheckDiskCacheAsync(domain);
            if (diskCacheResult != null)
            {
                _memoryCache[domain] = diskCacheResult;
                return diskCacheResult;
            }
            
            // Fetch favicon from web
            var iconData = await FetchFromWebAsync(uri);
            if (iconData != null)
            {
                // Cache both in memory and disk
                _memoryCache[domain] = iconData;
                await SaveToDiskCacheAsync(domain, iconData);
                
                _logger.LogDebug("Favicon fetched successfully for domain: {Domain}", domain);
                return iconData;
            }
            
            _logger.LogWarning("Failed to fetch favicon for domain: {Domain}", domain);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching favicon for URL: {Url}", url);
            return null;
        }
    }
    
    private async Task<IconData?> FetchFromWebAsync(Uri baseUri)
    {
        var domain = baseUri.Host;
        
        // Strategy 1: Try standard favicon paths
        var standardPaths = new[]
        {
            "/favicon.ico",
            "/favicon.png",
            "/apple-touch-icon.png",
            "/apple-touch-icon-precomposed.png"
        };
        
        foreach (var path in standardPaths)
        {
            try
            {
                var iconUri = new Uri(baseUri, path);
                var iconData = await DownloadIconAsync(iconUri);
                if (iconData != null)
                {
                    return iconData;
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Failed to fetch favicon from standard path: {Path}", path);
            }
        }
        
        // Strategy 2: Parse HTML for favicon links
        try
        {
            var htmlIconData = await ParseHtmlForFaviconAsync(baseUri);
            if (htmlIconData != null)
            {
                return htmlIconData;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to parse HTML for favicon: {Domain}", domain);
        }
        
        // Strategy 3: Use Google S2 service as fallback
        try
        {
            var googleS2Uri = new Uri($"https://www.google.com/s2/favicons?domain={domain}&sz=32");
            var googleIconData = await DownloadIconAsync(googleS2Uri);
            if (googleIconData != null)
            {
                _logger.LogDebug("Using Google S2 favicon for domain: {Domain}", domain);
                return googleIconData;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to fetch favicon from Google S2: {Domain}", domain);
        }
        
        return null;
    }
    
    private async Task<IconData?> DownloadIconAsync(Uri iconUri)
    {
        try
        {
            using var response = await _httpClient.GetAsync(iconUri);
            if (!response.IsSuccessStatusCode)
            {
                return null;
            }
            
            var contentType = response.Content.Headers.ContentType?.MediaType;
            if (!IsValidImageContentType(contentType))
            {
                return null;
            }
            
            var data = await response.Content.ReadAsByteArrayAsync();
            if (data.Length == 0 || data.Length > 1024 * 1024) // Max 1MB
            {
                return null;
            }
            
            // Validate image format
            if (!IsValidImageData(data))
            {
                return null;
            }
            
            return new IconData
            {
                Data = data,
                ContentType = contentType ?? "image/x-icon",
                SourceUrl = iconUri.ToString(),
                FetchedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to download icon from: {Url}", iconUri);
            return null;
        }
    }
    
    private async Task<IconData?> ParseHtmlForFaviconAsync(Uri baseUri)
    {
        try
        {
            using var response = await _httpClient.GetAsync(baseUri);
            if (!response.IsSuccessStatusCode)
            {
                return null;
            }
            
            var html = await response.Content.ReadAsStringAsync();
            var doc = new HtmlDocument();
            doc.LoadHtml(html);
            
            // Look for favicon link tags
            var linkNodes = doc.DocumentNode.SelectNodes("//link[@rel]");
            if (linkNodes == null) return null;
            
            var faviconLinks = linkNodes
                .Where(node => 
                {
                    var rel = node.GetAttributeValue("rel", "").ToLowerInvariant();
                    return rel.Contains("icon") || rel.Contains("shortcut icon");
                })
                .Select(node => node.GetAttributeValue("href", ""))
                .Where(href => !string.IsNullOrEmpty(href))
                .ToList();
            
            foreach (var href in faviconLinks)
            {
                try
                {
                    var iconUri = new Uri(baseUri, href);
                    var iconData = await DownloadIconAsync(iconUri);
                    if (iconData != null)
                    {
                        return iconData;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Failed to download favicon from HTML link: {Href}", href);
                }
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to parse HTML for favicon: {Url}", baseUri);
            return null;
        }
    }
    
    private bool IsValidImageContentType(string? contentType)
    {
        if (string.IsNullOrEmpty(contentType)) return true; // Allow unknown types
        
        var validTypes = new[]
        {
            "image/x-icon",
            "image/vnd.microsoft.icon",
            "image/png",
            "image/gif",
            "image/jpeg",
            "image/webp",
            "image/svg+xml"
        };
        
        return validTypes.Contains(contentType.ToLowerInvariant());
    }
    
    private bool IsValidImageData(byte[] data)
    {
        if (data.Length < 4) return false;
        
        // Check for common image file signatures
        var signatures = new Dictionary<string, byte[]>
        {
            ["ICO"] = new byte[] { 0x00, 0x00, 0x01, 0x00 },
            ["PNG"] = new byte[] { 0x89, 0x50, 0x4E, 0x47 },
            ["GIF"] = new byte[] { 0x47, 0x49, 0x46, 0x38 },
            ["JPEG"] = new byte[] { 0xFF, 0xD8, 0xFF },
            ["WEBP"] = new byte[] { 0x52, 0x49, 0x46, 0x46 }
        };
        
        return signatures.Values.Any(signature =>
            data.Take(signature.Length).SequenceEqual(signature));
    }
}

public class IconData
{
    public byte[] Data { get; set; } = Array.Empty<byte>();
    public string ContentType { get; set; } = string.Empty;
    public string SourceUrl { get; set; } = string.Empty;
    public DateTime FetchedAt { get; set; }
    public string LocalPath { get; set; } = string.Empty;
}
```

## Data Models

```csharp
public class AppModel
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public SessionMode SessionMode { get; set; } = SessionMode.Shared;
    public int Order { get; set; }
    public bool IsEnabled { get; set; } = true;
    public string? IconPath { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime? DeletedAt { get; set; }
    
    // Computed properties
    public string DisplayName => string.IsNullOrEmpty(Name) ? GetDomainFromUrl() : Name;
    public Uri Uri => new(Url);
    
    private string GetDomainFromUrl()
    {
        try
        {
            return new Uri(Url).Host;
        }
        catch
        {
            return Url;
        }
    }
}

public enum SessionMode
{
    Shared,
    Isolated
}

public class CreateAppRequest
{
    public string Name { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public SessionMode SessionMode { get; set; } = SessionMode.Shared;
}

public class UpdateAppRequest
{
    public string? Name { get; set; }
    public string? Url { get; set; }
    public SessionMode? SessionMode { get; set; }
    public bool? IsEnabled { get; set; }
}
```

## Database Schema

```sql
CREATE TABLE Apps (
    Id TEXT PRIMARY KEY,
    Name TEXT NOT NULL,
    Url TEXT NOT NULL,
    SessionMode INTEGER NOT NULL DEFAULT 0,
    [Order] INTEGER NOT NULL DEFAULT 0,
    IsEnabled INTEGER NOT NULL DEFAULT 1,
    IconPath TEXT NULL,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT NOT NULL,
    IsDeleted INTEGER NOT NULL DEFAULT 0,
    DeletedAt TEXT NULL
);

CREATE INDEX IX_Apps_Order ON Apps ([Order]) WHERE IsDeleted = 0;
CREATE INDEX IX_Apps_Name ON Apps (Name) WHERE IsDeleted = 0;
CREATE UNIQUE INDEX IX_Apps_Url ON Apps (Url) WHERE IsDeleted = 0;
```

## Testing Strategy

```csharp
[TestClass]
public class AppManagerServiceTests
{
    private AppManagerService _service;
    private Mock<IAppRepository> _repositoryMock;
    private Mock<FaviconService> _faviconMock;
    
    [TestInitialize]
    public void Setup()
    {
        _repositoryMock = new Mock<IAppRepository>();
        _faviconMock = new Mock<FaviconService>();
        _service = new AppManagerService(_repositoryMock.Object, _faviconMock.Object);
    }
    
    [TestMethod]
    public async Task CreateAppAsync_ValidRequest_CreatesApp()
    {
        // Arrange
        var request = new CreateAppRequest
        {
            Name = "Test App",
            Url = "https://example.com",
            SessionMode = SessionMode.Isolated
        };
        
        _repositoryMock.Setup(r => r.CreateAsync(It.IsAny<AppModel>()))
                      .ReturnsAsync((AppModel app) => app);
        
        // Act
        var result = await _service.CreateAppAsync(request);
        
        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(request.Name, result.Name);
        Assert.AreEqual("https://example.com/", result.Url);
        Assert.AreEqual(request.SessionMode, result.SessionMode);
        
        _repositoryMock.Verify(r => r.CreateAsync(It.IsAny<AppModel>()), Times.Once);
    }
}
```

## Dependencies

- `Microsoft.EntityFrameworkCore.Sqlite` - Database ORM
- `HtmlAgilityPack` - HTML parsing for favicon detection
- `System.ComponentModel.DataAnnotations` - Validation
- `Microsoft.Extensions.Caching.Memory` - In-memory caching 