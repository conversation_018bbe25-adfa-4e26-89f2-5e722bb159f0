# SideView Implementation Documentation Index

## Overview
This directory contains detailed implementation documentation for all SideView modules. Each module document provides comprehensive technical details, architectural diagrams, code examples, and testing strategies to help engineers understand and implement the system.

## Migration Status: C# .NET → TypeScript/Electron
**Status**: In Progress - Migrating from C# .NET to TypeScript/Electron for better cross-platform support and tooling.

The original C# .NET implementation has been analyzed and a comprehensive migration plan has been created. The new Electron-based implementation will maintain all core functionality while providing better development experience and cross-platform compatibility.

## Module Documentation

### [01. Core Module](01-core-module.md)
- **Purpose**: Foundation and orchestrator for the entire application
- **Key Components**: Application Host, Event Bus, Configuration Service
- **Highlights**: Generic Host pattern, event-driven architecture, configuration management
- **Dependencies**: Microsoft.Extensions.Hosting, System.Threading.Channels

### [02. UI Module](02-ui-module.md)
- **Purpose**: Panel window, animations, themes, and user interactions
- **Key Components**: <PERSON><PERSON><PERSON>ow, <PERSON><PERSON><PERSON><PERSON><PERSON>, AnimationController, ThemeManager
- **Highlights**: WPF + Composition API, 60fps animations, native Windows theming
- **Dependencies**: WPF, Windows Composition API, Win32 APIs

### [03. WebEngine Module](03-webengine-module.md)
- **Purpose**: Web content rendering and JavaScript bridge
- **Key Components**: WebAppHost, NavigationManager, JSBridge, EnvironmentManager
- **Highlights**: WebView2 integration, security policies, native-web communication
- **Dependencies**: Microsoft.Web.WebView2, System.Text.Json

### [04. AppManager Module](04-appmanager-module.md)
- **Purpose**: CRUD operations for web applications and favicon management
- **Key Components**: AppManagerService, AppRepository, FaviconService
- **Highlights**: Repository pattern, SQLite storage, intelligent favicon fetching
- **Dependencies**: Entity Framework Core, HtmlAgilityPack, SQLite

### [05. SessionManager Module](05-sessionmanager-module.md)
- **Purpose**: Cookie and cache isolation between applications
- **Key Components**: SessionManagerService, UserDataManager, CookieManager
- **Highlights**: Isolated vs shared sessions, data cleanup, security policies
- **Dependencies**: Microsoft.Data.Sqlite, System.IO.Compression

### [06. Notifications Module](06-notifications-module.md)
- **Purpose**: Bridge web notifications to native Windows toasts
- **Key Components**: NotificationService, ToastManager, BadgeManager
- **Highlights**: WebView2 notification integration, Windows toast APIs
- **Dependencies**: Microsoft.Toolkit.Win32.UI.Controls, Windows.UI.Notifications

### [07. Hotkeys Module](07-hotkeys-module.md)
- **Purpose**: Global hotkey registration and handling
- **Key Components**: HotkeyService, Win32 message handling
- **Highlights**: System-wide hotkey capture, conflict detection
- **Dependencies**: Microsoft.Windows.CsWin32, Win32 APIs

### [08. Updater Module](08-updater-module.md)
- **Purpose**: Automatic updates via GitHub releases
- **Key Components**: UpdaterService, Squirrel.Windows integration
- **Highlights**: Delta updates, background checking, seamless installation
- **Dependencies**: Squirrel, Octokit

## System Architecture Overview

```mermaid
graph TB
    subgraph "Core Layer"
        A[Core Module]
        A --> B[Event Bus]
        A --> C[Configuration]
        A --> D[Logging]
    end
    
    subgraph "UI Layer"
        E[UI Module]
        E --> F[Panel Window]
        E --> G[Animations]
        E --> H[Themes]
    end
    
    subgraph "Web Layer"
        I[WebEngine Module]
        I --> J[WebView2]
        I --> K[JS Bridge]
        I --> L[Navigation]
    end
    
    subgraph "Data Layer"
        M[AppManager Module]
        M --> N[SQLite DB]
        M --> O[Favicon Cache]
        P[SessionManager Module]
        P --> Q[User Data]
        P --> R[Cookies]
    end
    
    subgraph "System Integration"
        S[Notifications Module]
        T[Hotkeys Module]
        U[Updater Module]
    end
    
    A --> E
    A --> I
    A --> M
    A --> P
    A --> S
    A --> T
    A --> U
    
    E --> I
    I --> M
    I --> P
    I --> S
```

## Development Workflow

### 1. Setup Phase
1. Read [Core Module](01-core-module.md) to understand the foundation
2. Set up the solution structure as defined in the implementation proposal
3. Implement dependency injection and service registration

### 2. UI Development
1. Follow [UI Module](02-ui-module.md) for window and animation implementation
2. Implement theme system and activation engine
3. Test on multiple monitors and DPI settings

### 3. Web Integration
1. Implement [WebEngine Module](03-webengine-module.md) for WebView2 hosting
2. Set up JavaScript bridge for native-web communication
3. Implement security policies and navigation controls

### 4. Data Management
1. Follow [AppManager Module](04-appmanager-module.md) for app CRUD operations
2. Implement [SessionManager Module](05-sessionmanager-module.md) for data isolation
3. Set up database migrations and data validation

### 5. System Features
1. Implement [Notifications Module](06-notifications-module.md) for toast integration
2. Add [Hotkeys Module](07-hotkeys-module.md) for global shortcuts
3. Set up [Updater Module](08-updater-module.md) for automatic updates

## Testing Strategy

### Unit Testing
- Each module includes unit test examples
- Focus on business logic and data operations
- Mock external dependencies (file system, network, Win32 APIs)

### Integration Testing
- Test module interactions through the event bus
- Verify WebView2 integration and JavaScript bridge
- Test database operations and migrations

### UI Testing
- Use WinAppDriver for automated UI testing
- Test animation performance and responsiveness
- Verify theme switching and DPI scaling

### Performance Testing
- Memory usage monitoring (target: <100MB idle, <400MB active)
- Animation smoothness (target: 60fps)
- Startup time (target: <2 seconds)

## Security Considerations

### WebView2 Security
- All instances run in sandbox mode
- CSP headers injected for additional protection
- HTTPS enforcement for navigation
- Script injection prevention

### Data Protection
- User data stored in standard Windows locations
- Isolated sessions prevent data leakage
- Secure credential storage using Windows Credential Manager

### System Integration
- Code signing for all executables
- Minimal Win32 API usage with documented purposes
- No elevation required for normal operation

## Performance Guidelines

### Memory Management
- Use object pooling for frequent allocations
- Dispose resources properly (WebView2, timers, event handlers)
- Monitor memory usage with performance counters

### Threading
- All services are thread-safe with minimal locking
- Use async/await throughout for I/O operations
- Background tasks use CancellationToken for cleanup

### Resource Usage
- Lazy initialization of heavy components
- Cache frequently accessed data
- Minimize disk I/O and network requests

## Troubleshooting Common Issues

### WebView2 Issues
- Ensure WebView2 Runtime is installed (minimum version 119+)
- Check user data folder permissions
- Verify JavaScript bridge initialization

### UI Performance
- Monitor for UI thread blocking
- Check animation performance on different hardware
- Verify theme resources are loading correctly

### Data Issues
- Check SQLite database file permissions
- Verify favicon download and caching
- Monitor session data isolation

## Contributing Guidelines

### Code Style
- Follow C# 12 conventions with file-scoped namespaces
- Use nullable reference types throughout
- Prefer async/await over Task.Result or .Wait()

### Documentation
- Update module documentation when adding features
- Include mermaid diagrams for complex flows
- Add comprehensive code comments

### Testing
- Maintain >80% code coverage
- Add integration tests for new features
- Update performance benchmarks

## Deployment Checklist

### Pre-Release
- [ ] All modules implemented and tested
- [ ] Performance targets met
- [ ] Security review completed
- [ ] Code signing certificates configured

### Release Preparation
- [ ] MSIX package created and tested
- [ ] Fallback installer (self-contained EXE) prepared
- [ ] Update mechanism tested
- [ ] Documentation updated

### Post-Release
- [ ] Monitor crash reports and performance metrics
- [ ] Gather user feedback
- [ ] Plan next iteration based on usage patterns

This comprehensive documentation provides engineers with everything needed to understand, implement, and maintain the SideView application successfully. 