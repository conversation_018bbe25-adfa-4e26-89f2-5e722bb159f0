# SideView.Hotkeys Module Implementation

## Overview
The Hotkeys module provides global hotkey registration and handling using Win32 APIs, allowing users to control SideView from anywhere in the system.

## Architecture

```mermaid
graph TB
    A[HotkeyService] --> B[Win32 APIs]
    A --> C[HotkeyRegistry]
    A --> D[MessageLoop]
    
    B --> E[RegisterHotKey]
    B --> F[UnregisterHotKey]
    D --> G[WM_HOTKEY Handler]
    
    subgraph "Hotkey Flow"
        H[User Presses Keys]
        I[System Captures]
        J[Message Dispatched]
        K[Event Raised]
    end
    
    H --> I
    I --> J
    J --> D
    D --> K
```

## Key Components

### 1. Hotkey Service (`HotkeyService.cs`)

```mermaid
stateDiagram-v2
    [*] --> Unregistered
    Unregistered --> Registered: RegisterHotkey()
    Registered --> Triggered: Key Pressed
    Triggered --> Registered: Event Handled
    Registered --> Unregistered: UnregisterHotkey()
```

```csharp
public class HotkeyService : IHotkeyService, IDisposable
{
    private readonly ILogger<HotkeyService> _logger;
    private readonly Dictionary<int, HotkeyInfo> _registeredHotkeys;
    private readonly HwndSource _hwndSource;
    private int _nextHotkeyId = 1;
    
    public event EventHandler<HotkeyPressedEventArgs> HotkeyPressed;
    
    public HotkeyService(ILogger<HotkeyService> logger)
    {
        _logger = logger;
        _registeredHotkeys = new Dictionary<int, HotkeyInfo>();
        
        // Create hidden window for message handling
        _hwndSource = CreateMessageWindow();
        _hwndSource.AddHook(WndProc);
    }
    
    public bool RegisterHotkey(string name, ModifierKeys modifiers, Key key)
    {
        var hotkeyId = _nextHotkeyId++;
        var vKey = KeyInterop.VirtualKeyFromKey(key);
        
        if (RegisterHotKey(_hwndSource.Handle, hotkeyId, (uint)modifiers, (uint)vKey))
        {
            _registeredHotkeys[hotkeyId] = new HotkeyInfo
            {
                Id = hotkeyId,
                Name = name,
                Modifiers = modifiers,
                Key = key
            };
            
            _logger.LogInformation("Registered hotkey: {Name} ({Modifiers}+{Key})", 
                name, modifiers, key);
            return true;
        }
        
        _logger.LogWarning("Failed to register hotkey: {Name}", name);
        return false;
    }
    
    private IntPtr WndProc(IntPtr hwnd, int msg, IntPtr wParam, IntPtr lParam, ref bool handled)
    {
        if (msg == WM_HOTKEY)
        {
            var hotkeyId = wParam.ToInt32();
            if (_registeredHotkeys.TryGetValue(hotkeyId, out var hotkeyInfo))
            {
                HotkeyPressed?.Invoke(this, new HotkeyPressedEventArgs(hotkeyInfo));
                handled = true;
            }
        }
        
        return IntPtr.Zero;
    }
    
    [DllImport("user32.dll")]
    private static extern bool RegisterHotKey(IntPtr hWnd, int id, uint fsModifiers, uint vk);
    
    [DllImport("user32.dll")]
    private static extern bool UnregisterHotKey(IntPtr hWnd, int id);
    
    private const int WM_HOTKEY = 0x0312;
}
```

## Dependencies

- `Microsoft.Windows.CsWin32` - Win32 API access
- `System.Windows.Input` - Key definitions 