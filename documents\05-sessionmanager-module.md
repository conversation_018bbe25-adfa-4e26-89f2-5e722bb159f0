# SideView.SessionMgr Module Implementation

## Overview
The SessionManager module handles cookie and cache isolation between different web applications, providing both shared and isolated session modes with data cleanup capabilities.

## Architecture

```mermaid
graph TB
    A[SessionManagerService] --> B[UserDataManager]
    A --> C[CookieManager]
    A --> D[CacheManager]
    
    B --> E[Shared Profile]
    B --> F[App Profile 1]
    B --> G[App Profile N]
    
    C --> H[Cookie Store]
    D --> I[Cache Files]
    D --> J[LocalStorage]
    D --> K[IndexedDB]
    
    subgraph "Profile Structure"
        L[%LOCALAPPDATA%/SideView/Shared]
        M[%LOCALAPPDATA%/SideView/Apps/{AppId}]
    end
    
    E --> L
    F --> M
```

## Key Components

### 1. Session Manager Service (`SessionManagerService.cs`)

```mermaid
stateDiagram-v2
    [*] --> Initializing
    Initializing --> Ready: Setup Complete
    Ready --> Creating: CreateSession()
    Creating --> Configuring: Configure Profile
    Configuring --> Ready: Session Created
    
    Ready --> Cleaning: CleanupSession()
    Cleaning --> Ready: Cleanup Complete
    
    Ready --> Clearing: ClearData()
    Clearing --> Ready: Data Cleared
```

```csharp
public class SessionManagerService : ISessionManagerService
{
    private readonly ILogger<SessionManagerService> _logger;
    private readonly string _baseDataPath;
    private readonly ConcurrentDictionary<Guid, SessionInfo> _sessions;
    private readonly SemaphoreSlim _semaphore;
    
    public SessionManagerService(ILogger<SessionManagerService> logger)
    {
        _logger = logger;
        _baseDataPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
            "SideView");
        _sessions = new ConcurrentDictionary<Guid, SessionInfo>();
        _semaphore = new SemaphoreSlim(1, 1);
        
        InitializeDirectories();
    }
    
    public string GetUserDataFolder(AppModel app)
    {
        return app.SessionMode switch
        {
            SessionMode.Shared => Path.Combine(_baseDataPath, "Shared"),
            SessionMode.Isolated => Path.Combine(_baseDataPath, "Apps", app.Id.ToString()),
            _ => throw new ArgumentOutOfRangeException()
        };
    }
    
    public async Task<SessionInfo> CreateSessionAsync(AppModel app)
    {
        await _semaphore.WaitAsync();
        try
        {
            _logger.LogInformation("Creating session for app {AppId} in {Mode} mode", 
                app.Id, app.SessionMode);
            
            var userDataFolder = GetUserDataFolder(app);
            Directory.CreateDirectory(userDataFolder);
            
            var sessionInfo = new SessionInfo
            {
                AppId = app.Id,
                SessionMode = app.SessionMode,
                UserDataFolder = userDataFolder,
                CreatedAt = DateTime.UtcNow,
                LastAccessedAt = DateTime.UtcNow
            };
            
            // Configure session based on mode
            await ConfigureSessionAsync(sessionInfo, app);
            
            _sessions[app.Id] = sessionInfo;
            
            _logger.LogInformation("Session created successfully for app {AppId}", app.Id);
            return sessionInfo;
        }
        finally
        {
            _semaphore.Release();
        }
    }
    
    private async Task ConfigureSessionAsync(SessionInfo sessionInfo, AppModel app)
    {
        var userDataPath = sessionInfo.UserDataFolder;
        
        // Create necessary subdirectories
        var directories = new[]
        {
            Path.Combine(userDataPath, "Default"),
            Path.Combine(userDataPath, "Default", "Cache"),
            Path.Combine(userDataPath, "Default", "Cookies"),
            Path.Combine(userDataPath, "Default", "Local Storage"),
            Path.Combine(userDataPath, "Default", "Session Storage"),
            Path.Combine(userDataPath, "Default", "IndexedDB")
        };
        
        foreach (var dir in directories)
        {
            Directory.CreateDirectory(dir);
        }
        
        // Apply security settings for isolated sessions
        if (app.SessionMode == SessionMode.Isolated)
        {
            await ApplyIsolationSecurityAsync(userDataPath);
        }
        
        // Set up preferences
        await CreatePreferencesFileAsync(userDataPath, app);
    }
    
    private async Task ApplyIsolationSecurityAsync(string userDataPath)
    {
        // Create security policy file
        var securityPolicy = new
        {
            isolated = true,
            site_per_process = true,
            disable_background_sync = true,
            disable_background_fetch = true
        };
        
        var policyPath = Path.Combine(userDataPath, "security_policy.json");
        var json = JsonSerializer.Serialize(securityPolicy, new JsonSerializerOptions { WriteIndented = true });
        await File.WriteAllTextAsync(policyPath, json);
    }
    
    public async Task ClearDataAsync(Guid appId, SessionDataFlags flags)
    {
        if (!_sessions.TryGetValue(appId, out var sessionInfo))
        {
            _logger.LogWarning("Session not found for app {AppId}", appId);
            return;
        }
        
        _logger.LogInformation("Clearing session data for app {AppId}, flags: {Flags}", appId, flags);
        
        var userDataPath = sessionInfo.UserDataFolder;
        var defaultPath = Path.Combine(userDataPath, "Default");
        
        var tasks = new List<Task>();
        
        if (flags.HasFlag(SessionDataFlags.Cookies))
        {
            tasks.Add(ClearCookiesAsync(defaultPath));
        }
        
        if (flags.HasFlag(SessionDataFlags.Cache))
        {
            tasks.Add(ClearCacheAsync(defaultPath));
        }
        
        if (flags.HasFlag(SessionDataFlags.LocalStorage))
        {
            tasks.Add(ClearLocalStorageAsync(defaultPath));
        }
        
        if (flags.HasFlag(SessionDataFlags.SessionStorage))
        {
            tasks.Add(ClearSessionStorageAsync(defaultPath));
        }
        
        if (flags.HasFlag(SessionDataFlags.IndexedDB))
        {
            tasks.Add(ClearIndexedDBAsync(defaultPath));
        }
        
        await Task.WhenAll(tasks);
        
        sessionInfo.LastClearedAt = DateTime.UtcNow;
        _logger.LogInformation("Session data cleared successfully for app {AppId}", appId);
    }
    
    private async Task ClearCookiesAsync(string defaultPath)
    {
        var cookiesPath = Path.Combine(defaultPath, "Cookies");
        var cookiesJournalPath = Path.Combine(defaultPath, "Cookies-journal");
        
        await DeleteFilesSafelyAsync(new[] { cookiesPath, cookiesJournalPath });
    }
    
    private async Task ClearCacheAsync(string defaultPath)
    {
        var cachePath = Path.Combine(defaultPath, "Cache");
        if (Directory.Exists(cachePath))
        {
            await DeleteDirectorySafelyAsync(cachePath);
        }
    }
    
    private async Task DeleteFilesSafelyAsync(string[] filePaths)
    {
        foreach (var filePath in filePaths)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    _logger.LogDebug("Deleted file: {FilePath}", filePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to delete file: {FilePath}", filePath);
            }
        }
        
        await Task.Delay(100); // Small delay to ensure file system operations complete
    }
}

public class SessionInfo
{
    public Guid AppId { get; set; }
    public SessionMode SessionMode { get; set; }
    public string UserDataFolder { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime LastAccessedAt { get; set; }
    public DateTime? LastClearedAt { get; set; }
}

[Flags]
public enum SessionDataFlags
{
    None = 0,
    Cookies = 1,
    Cache = 2,
    LocalStorage = 4,
    SessionStorage = 8,
    IndexedDB = 16,
    All = Cookies | Cache | LocalStorage | SessionStorage | IndexedDB
}
```

### 2. Cookie Manager (`CookieManager.cs`)

```mermaid
sequenceDiagram
    participant A as App
    participant C as CookieManager
    participant S as SQLite Store
    participant F as File System
    
    A->>C: ImportCookies(source)
    C->>S: Read source cookies
    S->>C: Cookie data
    C->>F: Write to target store
    F->>C: Success
    C->>A: Import complete
    
    A->>C: ExportCookies(target)
    C->>S: Read app cookies
    S->>C: Cookie data
    C->>F: Write to export file
    F->>C: Success
    C->>A: Export complete
```

### 3. Data Migration (`SessionMigrationService.cs`)

```mermaid
flowchart TD
    A[Migration Request] --> B{From Mode}
    B -->|Shared→Isolated| C[Create Isolated Profile]
    B -->|Isolated→Shared| D[Merge to Shared Profile]
    
    C --> E[Copy Data Files]
    D --> F[Backup Current Data]
    
    E --> G[Update References]
    F --> G
    
    G --> H[Verify Migration]
    H --> I{Success?}
    I -->|Yes| J[Cleanup Old Data]
    I -->|No| K[Rollback Changes]
    
    J --> L[Migration Complete]
    K --> L
```

## Dependencies

- `Microsoft.Data.Sqlite` - Cookie database access
- `System.IO.Compression` - Archive operations
- `Microsoft.Extensions.Logging` - Logging 