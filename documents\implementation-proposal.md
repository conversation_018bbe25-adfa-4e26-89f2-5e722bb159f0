# SideView — Development Proposal

*A Slidepad-style sidebar browser for Windows*

## 1. Scope & Objectives

Build a native Windows desktop application that presents a slide-in browser panel (left or right edge) designed for quick access to web apps. The proposal covers **architecture, technology stack, project structure, coding conventions, test & release strategy, and operational concerns**—omitting scheduling.

## 2. Target Platform & System Requirements

| Item    | Spec                                            |
|---------|------------------------------------------------|
| OS      | Windows 10 (22H2+) / Windows 11                 |
| CPU     | x64 & Arm64                                     |
| Memory  | ≤ 100 MB idle, ≤ 400 MB typical workload        |
| DPI     | 100% – 300% with per-monitor-DPI awareness v2   |
| Network | Any; graceful degradation offline               |

## 3. Technology Stack

| Concern          | Choice                                                        | Rationale                                      |
|------------------|---------------------------------------------------------------|------------------------------------------------|
| **Language**     | C# 12                                                         | Modern features, large talent pool             |
| **Runtime**      | .NET 8 LTS                                                    | Native AOT option, single-file EXE, trimming   |
| **UI Framework** | WPF + WinUI 3 controls via XAML Islands                       | Rich styling, Composition animations, high-DPI |
| **Web Engine**   | Microsoft Edge WebView2 Evergreen                             | Chromium-based, first-class Windows support    |
| **Data**         | SQLite (Microsoft.Data.Sqlite) + Dapper                       | Lightweight ACID store; no daemon              |
| **Settings**     | JSON (System.Text.Json) in `%AppData%\SideView\settings.json` |                                                |
| **DI / IOC**     | Microsoft.Extensions.DependencyInjection                      |                                                |
| **Logging**      | Serilog → rolling JSON files → optional Sentry Transport      |                                                |
| **Updater**      | Squirrel .Windows (MSI bootstrap)                             |                                                |
| **Packaging**    | MSIX (primary) + self-contained EXE fallback                  |                                                |
| **Build**        | dotnet SDK → GitHub Actions → Code Signing (EV)               |                                                |

## 4. Solution & Project Layout

```
SideView.sln
├─ src/
│   ├─ SideView.Core/         # lifecycle, DI, config, logging
│   ├─ SideView.UI/           # WPF host, XAML, themes, animations
│   ├─ SideView.WebEngine/    # WebView2 hosting, tab management
│   ├─ SideView.AppManager/   # CRUD for web-app entries, repo
│   ├─ SideView.SessionMgr/   # cookie/cache isolation, cleanup
│   ├─ SideView.Notifications/# WinRT toast bridge, badges
│   ├─ SideView.Hotkeys/      # global hot-key service, P/Invoke
│   ├─ SideView.Updater/      # update check & apply
│   └─ SideView.Tests/        # unit & integration tests
└─ build/
    └─ pipelines/             # CI scripts, WiX/MSIX templates
```

### Coding Style:
- C# 12, `file-scoped namespace`, `readonly record struct` where applicable
- Async-first (ConfigureAwait(false))
- MVVM + CommunityToolkit.Mvvm for UI
- Nullable context **enabled** (`<Nullable>enable</Nullable>`)

## 5. Module Implementation Highlights

### 5.1 Core (`SideView.Core`)
- Entry-point hosts **Generic Host** (`Host.CreateDefaultBuilder`)
- Registers services, loads persisted state, wires logging sinks
- Publishes `IAppEventBus` (Channel-based) for decoupled communications

### 5.2 UI & Window Management (`SideView.UI`)
- **PanelWindow.xaml** – borderless WPF window sized `Width = Settings.PanelWidth` and bound to `ScreenEdge`
- Slide animation via **Windows Composition API** (`ScalarKeyFrameAnimation`) for 60 fps
- **Activation Engine**
  - Mouse: global low-level hook (Win32 `WH_MOUSE_LL`) with hit-test strip of *N* pixels
  - Hot-key: `RegisterHotKey` (`Mod + Key`), dispatch to event bus
- Window zoning: `TOPMOST | TOOLWINDOW`, toggled when pinned

### 5.3 Web Rendering (`SideView.WebEngine`)

```csharp
class WebAppHost : UserControl
{
    readonly CoreWebView2Environment _env;
    public Task InitializeAsync(AppModel app)
    {
        _env = await CoreWebView2Environment.CreateAsync(
                    userDataFolder: Path.Combine(ProfileRoot, app.Id));
        await WebView.EnsureCoreWebView2Async(_env);
        WebView.CoreWebView2.SetVirtualHostNameToFolderMapping(
             "sideview-assets", AssetDir, CoreWebView2HostResourceAccessKind.Deny);
        /* navigation, JS bridge, etc. */
    }
}
```

### 5.4 App Management (`SideView.AppManager`)
- `AppModel { Guid Id; string Name; string Url; SessionType SessionMode; }`
- Repository pattern over SQLite; Dapper CRUD
- Favicon fetch: HTTP `GET /favicon.ico`; fallback to Google S2 service; cache PNG in `AppData\Icons`

### 5.5 Session & Cache (`SideView.SessionMgr`)
- Maps `SessionMode` → `WebView2` `UserDataFolder`
  - **Shared:** `%LOCALAPPDATA%\SideView\SharedProfile`
  - **Isolated:** `%LOCALAPPDATA%\SideView\Profiles\{AppId}`
- Exposes `ClearDataAsync(appId, flags)` to UI

### 5.6 Notifications (`SideView.Notifications`)
- Enable WebView2 `CoreWebView2PermissionKind.Notifications`
- Implement `INotificationService`:
  - Converts `NotificationEvent` → Toast XML via **Windows App SDK**
  - Maintains per-app badge count in memory + disk
- Global mute → unregister toast channel; per-app mute → block event routing

### 5.7 Global Hotkey (`SideView.Hotkeys`)
- Service registers user-defined sequence in registry-safe format
- Uses `WM_HOTKEY` message pump in hidden window; event bus dispatch

### 5.8 Updater (`SideView.Updater`)
- Background task every 6h: hit GitHub Releases API → compare `semver`
- On acceptance: download delta, verify SHA-256, invoke Squirrel `Update.exe`
- UI prompt offers **Restart Now / Later**

## 6. Cross-Cutting Concerns

| Aspect                   | Plan                                                                                    |
|-------------------------|-----------------------------------------------------------------------------------------|
| **Error Handling**      | Polly-wrapped retries, global WPF `DispatcherUnhandledException`, native SEH translator |
| **Telemetry (opt-in)** | EventSource → OpenTelemetry → OTLP HTTP → self-hosted ClickHouse                        |
| **Accessibility**       | UIA names/roles, high-contrast check, keyboard navigation                               |
| **Security**           | All WebView2 instances in **sandbox**; CSP header injection; HTTPS enforcement          |
| **Internationalization**| `resx` resources, pseudo-loc build check                                                |
| **Theming**            | Two ResourceDictionaries; read `AppsUseLightTheme` registry                             |
| **Power & Battery**    | Suspend background refresh on `PBT_APMSUSPEND`                                          |

## 7. Development Workflow

| Stage                 | Tooling                                                    |
|----------------------|------------------------------------------------------------|
| **Static Analysis**  | Roslyn analyzers, StyleCop + ReSharper 2025, .editorconfig |
| **Unit Tests**       | xUnit + FluentAssertions; run on push/PR                   |
| **Integration Tests**| WinAppDriver (UI) in GH-Actions Windows VM                 |
| **Perf Benchmarks**  | BenchmarkDotNet; gated under `Release+Profile`             |
| **Code Review**      | GitHub PRs + mandatory two approvals                       |
| **Artifacts**        | CI produces `.msix` and `.exe` (self-contained, trimmed)   |

## 8. Deployment & Distribution

1. **Primary** – MSIX package (auto-updates via `AppInstaller` XML)
2. **Fallback** – Signed self-extracting EXE using Squirrel .Windows
3. **Silent Install Flags** for enterprise (`/quiet /norestart /log install.log`)

## 9. Risk & Mitigation

| Risk                                         | Mitigation                                                               |
|----------------------------------------------|--------------------------------------------------------------------------|
| WebView2 breaking changes                    | Pin minimum Runtime version, CI canary build                             |
| Global hooks flagged by AV                   | Microsoft Store submission, code-signing, documented APIs                |
| Resource creep on many tabs                  | Limit concurrent WebView2s; hibernate inactive; expose "Reload on Focus" |
| Multi-monitor edge detection inconsistencies | Use `GetMonitorInfo` and store per-monitor settings                     |

## 10. Future-Ready Extension Points

- **Plugin SDK** (C# scripting via Roslyn) to inject CSS/JS
- **Cloud Sync** of app list via OneDrive / optional K/V backend
- **Workspaces**: serialize sets, expose via command-line arg `--workspace <name>` for automation

## Conclusion

The proposed structure delivers a maintainable, performant Windows sidebar browser, leveraging modern .NET tooling, WebView2, and Windows-native UX paradigms. Each module has clear responsibilities, interoperates via a light event bus, and is accompanied by test, build, and update infrastructure suitable for continuous delivery.
